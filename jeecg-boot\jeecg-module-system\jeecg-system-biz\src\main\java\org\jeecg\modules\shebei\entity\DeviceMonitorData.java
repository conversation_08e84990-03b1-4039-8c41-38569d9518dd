package org.jeecg.modules.shebei.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备监控数据实体类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@TableName("device_monitor_data")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class DeviceMonitorData implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 设备ID
     */
    @Excel(name = "设备ID", width = 15)
    private String deviceId;
    
    /**
     * 设备编号
     */
    @Excel(name = "设备编号", width = 15)
    private String deviceCode;
    
    /**
     * 监测项目名称
     */
    @Excel(name = "监测项目", width = 15)
    private String monitorItem;
    
    /**
     * 监测值
     */
    @Excel(name = "监测值", width = 15)
    private BigDecimal monitorValue;
    
    /**
     * 单位
     */
    @Excel(name = "单位", width = 10)
    private String unit;
    
    /**
     * 正常范围最小值
     */
    @Excel(name = "最小值", width = 15)
    private BigDecimal minValue;
    
    /**
     * 正常范围最大值
     */
    @Excel(name = "最大值", width = 15)
    private BigDecimal maxValue;
    
    /**
     * 状态：0-正常，1-预警，2-异常
     */
    @Excel(name = "状态", width = 10)
    private Integer status;
    
    /**
     * 监测时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "监测时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date monitorTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
