package org.jeecg.modules.shebei.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.shebei.entity.DeviceMonitor;
import org.jeecg.modules.shebei.mapper.DeviceMonitorDataMapper;
import org.jeecg.modules.shebei.mapper.DeviceMonitorMapper;
import org.jeecg.modules.shebei.service.IDeviceMonitorService;
import org.jeecg.modules.shebei.vo.DeviceMonitorDashboardVO;
import org.jeecg.modules.shebei.vo.DeviceStatusStatVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备监控Service实现类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
public class DeviceMonitorServiceImpl extends ServiceImpl<DeviceMonitorMapper, DeviceMonitor> implements IDeviceMonitorService {
    
    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    
    @Autowired
    private DeviceMonitorDataMapper deviceMonitorDataMapper;
    
    @Override
    public DeviceMonitorDashboardVO getDashboardData() {
        DeviceMonitorDashboardVO dashboardVO = new DeviceMonitorDashboardVO();
        
        // 获取设备状态统计
        List<DeviceStatusStatVO> statusStat = getDeviceStatusStat();
        dashboardVO.setDeviceStatusStat(statusStat);
        
        // 获取设备运行状态统计
        dashboardVO.setDeviceRunStatusStat(getDeviceRunStatusStat());
        
        // 获取设备类型统计
        dashboardVO.setDeviceTypeStat(getDeviceTypeStat());
        
        // 获取在线设备列表
        dashboardVO.setOnlineDevices(getOnlineDevices(10));
        
        // 获取异常设备列表
        dashboardVO.setAbnormalDevices(getAbnormalDevices());
        
        // 获取监测数据分析
        dashboardVO.setMonitorDataAnalysis(deviceMonitorDataMapper.getMonitorDataAnalysis());
        
        // 计算统计数据
        int totalDevices = Math.toIntExact(this.count());
        int onlineDevicesCount = 0;
        int abnormalDevicesCount = 0;
        
        for (DeviceStatusStatVO stat : statusStat) {
            if (stat.getStatus() == 1) {
                onlineDevicesCount = stat.getCount();
            } else if (stat.getStatus() == 2 || stat.getStatus() == 3) {
                abnormalDevicesCount += stat.getCount();
            }
        }
        
        dashboardVO.setTotalDevices(totalDevices);
        dashboardVO.setOnlineDevicesCount(onlineDevicesCount);
        dashboardVO.setAbnormalDevicesCount(abnormalDevicesCount);
        
        // 计算在线率
        if (totalDevices > 0) {
            BigDecimal rate = new BigDecimal(onlineDevicesCount)
                    .divide(new BigDecimal(totalDevices), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100));
            dashboardVO.setOnlineRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            dashboardVO.setOnlineRate("0%");
        }
        
        return dashboardVO;
    }
    
    @Override
    public List<DeviceStatusStatVO> getDeviceStatusStat() {
        return deviceMonitorMapper.getDeviceStatusStat();
    }
    
    @Override
    public List<Map<String, Object>> getDeviceRunStatusStat() {
        return deviceMonitorMapper.getDeviceRunStatusStat();
    }
    
    @Override
    public List<Map<String, Object>> getDeviceTypeStat() {
        return deviceMonitorMapper.getDeviceTypeStat();
    }
    
    @Override
    public List<DeviceMonitor> getOnlineDevices(Integer limit) {
        return deviceMonitorMapper.getOnlineDevices(limit);
    }
    
    @Override
    public List<DeviceMonitor> getAbnormalDevices() {
        return deviceMonitorMapper.getAbnormalDevices();
    }
    
    @Override
    public boolean updateDeviceStatus(String deviceId, Integer status, Integer runStatus) {
        UpdateWrapper<DeviceMonitor> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", deviceId);
        updateWrapper.set("status", status);
        updateWrapper.set("run_status", runStatus);
        updateWrapper.set("last_update_time", new Date());
        return this.update(updateWrapper);
    }
    
    @Override
    public boolean batchUpdateLastTime(List<String> deviceIds) {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return true;
        }
        UpdateWrapper<DeviceMonitor> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", deviceIds);
        updateWrapper.set("last_update_time", new Date());
        return this.update(updateWrapper);
    }
}
