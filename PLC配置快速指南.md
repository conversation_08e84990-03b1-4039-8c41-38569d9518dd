# PLC配置快速指南

## 🚀 快速配置您的PLC

现在您有了PLC的IP和端口，可以通过以下两种方式进行配置：

### 方式一：通过管理界面配置（推荐）

1. **访问PLC配置管理页面**
   - 登录系统后，进入菜单：`设备监控` → `PLC配置管理`
   - 或直接访问：`/shebei/plc/config`

2. **添加PLC配置**
   - 点击"新增PLC"按钮
   - 填写以下信息：
     ```
     PLC名称：您的PLC名称（如：变电站主控PLC）
     IP地址：您的PLC IP地址
     端口：您的PLC端口（S7协议默认102）
     机架号：通常为0
     插槽号：CPU插槽号，通常为1
     连接超时：5000毫秒（默认）
     读取超时：3000毫秒（默认）
     启用状态：启用
     备注：可选的描述信息
     ```

3. **测试连接**
   - 保存配置后，点击"测试连接"按钮
   - 确认连接成功

### 方式二：直接在数据库中配置

如果您更喜欢直接操作数据库，可以执行以下SQL：

```sql
-- 替换下面的值为您的实际配置
INSERT INTO `plc_config` (
    `id`, 
    `plc_name`, 
    `ip_address`, 
    `port`, 
    `rack`, 
    `slot`, 
    `connection_timeout`, 
    `read_timeout`, 
    `enabled`, 
    `connection_status`, 
    `remark`, 
    `create_by`, 
    `create_time`
) VALUES (
    'your_plc_001',                    -- 唯一ID
    '您的PLC名称',                      -- PLC名称
    '*************',                   -- 您的PLC IP地址
    102,                               -- 您的PLC端口
    0,                                 -- 机架号
    1,                                 -- 插槽号
    5000,                              -- 连接超时(毫秒)
    3000,                              -- 读取超时(毫秒)
    1,                                 -- 启用状态：1-启用
    0,                                 -- 连接状态：0-未连接
    '生产环境PLC',                      -- 备注
    'admin',                           -- 创建人
    NOW()                              -- 创建时间
);
```

## 📋 配置参数说明

| 参数 | 说明 | 默认值 | 备注 |
|------|------|--------|------|
| PLC名称 | 便于识别的名称 | - | 必填，唯一 |
| IP地址 | PLC的网络地址 | - | 必填，格式：************* |
| 端口 | S7协议端口 | 102 | S7协议标准端口 |
| 机架号 | PLC机架编号 | 0 | 通常为0 |
| 插槽号 | CPU插槽编号 | 1 | 根据PLC配置确定 |
| 连接超时 | 连接超时时间 | 5000ms | 可根据网络情况调整 |
| 读取超时 | 数据读取超时 | 3000ms | 可根据PLC响应速度调整 |

## 🔧 配置数据点位

配置好PLC连接后，还需要配置数据点位映射：

### 1. 确定PLC数据结构

首先需要了解您的PLC中数据的存储位置，例如：
- 电压数据存储在：DB1.DBD0（数据块1，双字偏移0）
- 电流数据存储在：DB1.DBD4（数据块1，双字偏移4）
- 温度数据存储在：DB1.DBD8（数据块1，双字偏移8）

### 2. 配置数据点位

```sql
-- 配置电压监控点位
INSERT INTO `plc_data_point` (
    `id`, `plc_id`, `device_id`, `point_name`, `point_desc`,
    `data_area`, `db_number`, `start_address`, `data_type`,
    `unit`, `scale_factor`, `offset`, `monitor_item`, `enabled`,
    `min_value`, `max_value`, `create_by`, `create_time`
) VALUES (
    'point_voltage_001', 'your_plc_001', 'device_001', 'voltage', '电压',
    'DB', 1, 0, 'REAL',
    'V', 1, 0, '电压', 1,
    0, 550, 'admin', NOW()
);

-- 配置电流监控点位
INSERT INTO `plc_data_point` (
    `id`, `plc_id`, `device_id`, `point_name`, `point_desc`,
    `data_area`, `db_number`, `start_address`, `data_type`,
    `unit`, `scale_factor`, `offset`, `monitor_item`, `enabled`,
    `min_value`, `max_value`, `create_by`, `create_time`
) VALUES (
    'point_current_001', 'your_plc_001', 'device_001', 'current', '电流',
    'DB', 1, 4, 'REAL',
    'A', 1, 0, '电流', 1,
    0, 1000, 'admin', NOW()
);
```

## 🔍 验证配置

### 1. 检查PLC连接状态

在PLC配置管理页面中：
- 查看连接状态是否为"连接"
- 点击"测试连接"确认通信正常

### 2. 手动采集数据

在设备监控管理页面中：
- 找到对应的设备
- 点击"手动采集"按钮
- 查看是否能成功获取数据

### 3. 查看监控大屏

访问监控大屏页面：
- 查看设备状态是否正常更新
- 确认实时数据显示正确

## 🚨 常见问题排查

### 1. 连接失败
- 检查网络连通性：`ping PLC_IP`
- 确认PLC IP地址和端口正确
- 检查防火墙设置
- 确认PLC启用了S7通信

### 2. 数据读取失败
- 确认数据块存在于PLC中
- 检查地址配置是否正确
- 验证数据类型匹配
- 确认PLC访问权限

### 3. 数据不更新
- 检查数据采集任务是否启用
- 查看系统日志中的错误信息
- 确认设备状态为"在线"

## 📞 技术支持

如果遇到配置问题，请检查：

1. **系统日志**：查看详细的错误信息
2. **网络连接**：确保服务器能访问PLC
3. **PLC配置**：确认PLC端的通信设置
4. **数据格式**：确认数据类型和地址映射正确

配置完成后，系统将每30秒自动从PLC采集数据，并在监控大屏中实时显示。
