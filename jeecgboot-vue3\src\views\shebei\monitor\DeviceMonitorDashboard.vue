<template>
  <div class="device-monitor-dashboard">
    <!-- 头部标题 -->
    <div class="dashboard-header">
      <div class="header-title">
        <h1>设备状态检修</h1>
        <div class="subtitle">xxx变电站xxx设备名称</div>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <div class="stat-icon online"></div>
          <div class="stat-text">
            <div class="stat-label">设备全部数量在线情况</div>
            <div class="stat-value">{{ dashboardData.onlineRate || '0%' }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <!-- 设备列表 -->
        <div class="device-list-section">
          <h3>设备列表</h3>
          <div class="device-list">
            <div 
              v-for="device in dashboardData.onlineDevices" 
              :key="device.id"
              class="device-item"
              :class="getDeviceStatusClass(device.status)"
              @click="selectDevice(device)"
            >
              <div class="device-info">
                <div class="device-name">{{ device.deviceName }}</div>
                <div class="device-time">检测日期：{{ formatDate(device.lastUpdateTime) }}</div>
              </div>
              <div class="device-status">
                <span :class="getStatusClass(device.status)">{{ getStatusText(device.status) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 负载能力图表 -->
        <div class="chart-section">
          <h3>负载能力</h3>
          <div class="chart-container">
            <div ref="loadChart" class="chart"></div>
            <div class="chart-status">正常</div>
          </div>
        </div>
      </div>

      <!-- 中间3D设备模型 -->
      <div class="center-panel">
        <div class="device-model">
          <div class="device-3d-placeholder">
            <div class="device-3d-content">
              <div class="device-main"></div>
              <div class="device-parts">
                <div class="part part-1"></div>
                <div class="part part-2"></div>
                <div class="part part-3"></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 底部状态指示器 -->
        <div class="status-indicators">
          <div class="indicator">
            <div class="indicator-label">负载能力</div>
            <div class="indicator-status normal">正常</div>
          </div>
          <div class="indicator">
            <div class="indicator-label">油位清洁分析</div>
            <div class="indicator-status normal">正常</div>
          </div>
          <div class="indicator">
            <div class="indicator-label">制冷寿命</div>
            <div class="indicator-status warning">XX年</div>
          </div>
          <div class="indicator">
            <div class="indicator-label">智能评价</div>
            <div class="indicator-status normal">正常</div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <!-- 设备状态 -->
        <div class="device-status-section">
          <h3>设备的状态</h3>
          <div class="status-card">
            <div class="status-icon healthy"></div>
            <div class="status-info">
              <div class="status-title">健康</div>
              <div class="status-time">上次评估：{{ formatDateTime(selectedDevice?.lastUpdateTime) }}</div>
              <div class="status-detail">正常</div>
            </div>
          </div>
        </div>

        <!-- 运行信息 -->
        <div class="running-info-section">
          <h3>运行信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">负荷</span>
              <span class="info-value">0MW</span>
              <span class="info-unit">电流</span>
              <span class="info-value">0.00A</span>
            </div>
            <div class="info-item">
              <span class="info-label">电压</span>
              <span class="info-value">500V</span>
              <span class="info-unit">温度</span>
              <span class="info-value">35°C</span>
            </div>
          </div>
        </div>

        <!-- 监测数据分析 -->
        <div class="analysis-section">
          <h3>监测数据分析</h3>
          <div class="analysis-table">
            <div class="table-header">
              <span>监测项目名称</span>
              <span>数据</span>
              <span>单位</span>
              <span>状态</span>
            </div>
            <div 
              v-for="item in monitorAnalysis" 
              :key="item.monitor_item"
              class="table-row"
            >
              <span>{{ item.monitor_item }}</span>
              <span>{{ item.normal_count }}</span>
              <span>个</span>
              <span class="status-normal">正常</span>
            </div>
          </div>
        </div>

        <!-- 需要目标 -->
        <div class="target-section">
          <h3>需要目标</h3>
          <div class="target-list">
            <div class="target-item">
              <span class="target-date">2017-02-03</span>
              <span class="target-action">例行</span>
            </div>
            <div class="target-item">
              <span class="target-date">2020-12-03</span>
              <span class="target-action">例行</span>
            </div>
            <div class="target-item">
              <span class="target-date">2020-09-22</span>
              <span class="target-action">例行</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { getDashboardData, getLatestData } from './api'

// 响应式数据
const dashboardData = ref<any>({})
const selectedDevice = ref<any>(null)
const monitorAnalysis = ref<any[]>([])
const loadChart = ref<HTMLElement>()

// 获取大屏数据
const fetchDashboardData = async () => {
  try {
    const result = await getDashboardData()
    if (result.success) {
      dashboardData.value = result.result
      monitorAnalysis.value = result.result.monitorDataAnalysis || []
      if (result.result.onlineDevices && result.result.onlineDevices.length > 0) {
        selectedDevice.value = result.result.onlineDevices[0]
      }
    }
  } catch (error) {
    console.error('获取大屏数据失败:', error)
  }
}

// 选择设备
const selectDevice = async (device: any) => {
  selectedDevice.value = device
  try {
    const result = await getLatestData(device.id)
    if (result.success) {
      // 处理最新数据
      console.log('设备最新数据:', result.result)
    }
  } catch (error) {
    console.error('获取设备数据失败:', error)
  }
}

// 初始化负载能力图表
const initLoadChart = () => {
  if (!loadChart.value) return
  
  const chart = echarts.init(loadChart.value)
  const option = {
    series: [{
      type: 'bar',
      data: [
        { value: 80, itemStyle: { color: '#00ff00' } },
        { value: 65, itemStyle: { color: '#ffff00' } },
        { value: 90, itemStyle: { color: '#00ff00' } },
        { value: 45, itemStyle: { color: '#ff0000' } },
        { value: 75, itemStyle: { color: '#00ff00' } },
        { value: 85, itemStyle: { color: '#00ff00' } },
        { value: 55, itemStyle: { color: '#ffff00' } }
      ],
      barWidth: 8
    }],
    xAxis: { show: false },
    yAxis: { show: false },
    grid: { top: 0, bottom: 0, left: 0, right: 0 }
  }
  chart.setOption(option)
}

// 工具函数
const getDeviceStatusClass = (status: number) => {
  switch (status) {
    case 1: return 'online'
    case 2: return 'fault'
    case 3: return 'maintenance'
    default: return 'offline'
  }
}

const getStatusClass = (status: number) => {
  switch (status) {
    case 1: return 'status-online'
    case 2: return 'status-fault'
    case 3: return 'status-maintenance'
    default: return 'status-offline'
  }
}

const getStatusText = (status: number) => {
  switch (status) {
    case 1: return '正常'
    case 2: return '故障'
    case 3: return '维护'
    default: return '离线'
  }
}

const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

const formatDateTime = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}

// 生命周期
onMounted(async () => {
  await fetchDashboardData()
  await nextTick()
  initLoadChart()
})
</script>

<style scoped lang="less">
.device-monitor-dashboard {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0c1e3f 0%, #1a365d 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;
    background: rgba(0, 0, 0, 0.3);
    border-bottom: 2px solid #00ffff;

    .header-title {
      h1 {
        font-size: 32px;
        font-weight: bold;
        margin: 0;
        color: #00ffff;
        text-shadow: 0 0 10px #00ffff;
      }

      .subtitle {
        font-size: 16px;
        color: #ffffff;
        margin-top: 5px;
      }
    }

    .header-stats {
      .stat-item {
        display: flex;
        align-items: center;
        gap: 15px;

        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #00ff00;
          box-shadow: 0 0 15px #00ff00;

          &.online {
            background: #00ff00;
            box-shadow: 0 0 15px #00ff00;
          }
        }

        .stat-text {
          .stat-label {
            font-size: 14px;
            color: #cccccc;
          }

          .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #00ffff;
          }
        }
      }
    }
  }

  .dashboard-content {
    display: flex;
    height: calc(100vh - 100px);
    padding: 20px;
    gap: 20px;

    .left-panel {
      width: 300px;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .device-list-section {
        background: rgba(0, 0, 0, 0.4);
        border: 1px solid #00ffff;
        border-radius: 8px;
        padding: 15px;

        h3 {
          color: #00ffff;
          margin: 0 0 15px 0;
          font-size: 18px;
        }

        .device-list {
          max-height: 300px;
          overflow-y: auto;

          .device-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              background: rgba(0, 255, 255, 0.2);
            }

            &.online {
              border-left: 4px solid #00ff00;
            }

            &.fault {
              border-left: 4px solid #ff0000;
            }

            &.maintenance {
              border-left: 4px solid #ffff00;
            }

            &.offline {
              border-left: 4px solid #666666;
            }

            .device-info {
              .device-name {
                font-size: 14px;
                font-weight: bold;
              }

              .device-time {
                font-size: 12px;
                color: #cccccc;
                margin-top: 2px;
              }
            }

            .device-status {
              .status-online { color: #00ff00; }
              .status-fault { color: #ff0000; }
              .status-maintenance { color: #ffff00; }
              .status-offline { color: #666666; }
            }
          }
        }
      }

      .chart-section {
        background: rgba(0, 0, 0, 0.4);
        border: 1px solid #00ffff;
        border-radius: 8px;
        padding: 15px;

        h3 {
          color: #00ffff;
          margin: 0 0 15px 0;
          font-size: 18px;
        }

        .chart-container {
          position: relative;

          .chart {
            width: 100%;
            height: 120px;
          }

          .chart-status {
            text-align: center;
            color: #00ff00;
            font-weight: bold;
            margin-top: 10px;
          }
        }
      }
    }

    .center-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .device-model {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        .device-3d-placeholder {
          width: 400px;
          height: 300px;
          perspective: 1000px;

          .device-3d-content {
            position: relative;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            animation: rotate3d 10s infinite linear;

            .device-main {
              position: absolute;
              top: 50%;
              left: 50%;
              width: 200px;
              height: 150px;
              background: linear-gradient(45deg, #333, #666);
              border: 2px solid #00ffff;
              border-radius: 10px;
              transform: translate(-50%, -50%) rotateX(15deg);
              box-shadow:
                0 0 20px #00ffff,
                inset 0 0 20px rgba(0, 255, 255, 0.2);

              &::before {
                content: '';
                position: absolute;
                top: 20px;
                left: 20px;
                width: 30px;
                height: 30px;
                background: #ff4444;
                border-radius: 50%;
                box-shadow: 0 0 10px #ff4444;
                animation: blink 2s infinite;
              }

              &::after {
                content: '';
                position: absolute;
                top: 20px;
                right: 20px;
                width: 30px;
                height: 30px;
                background: #44ff44;
                border-radius: 50%;
                box-shadow: 0 0 10px #44ff44;
                animation: blink 1.5s infinite;
              }
            }

            .device-parts {
              .part {
                position: absolute;
                background: linear-gradient(45deg, #444, #777);
                border: 1px solid #00ffff;
                box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
              }

              .part-1 {
                top: 30%;
                left: 20%;
                width: 60px;
                height: 80px;
                border-radius: 5px;
                transform: rotateY(30deg);
              }

              .part-2 {
                top: 30%;
                right: 20%;
                width: 60px;
                height: 80px;
                border-radius: 5px;
                transform: rotateY(-30deg);
              }

              .part-3 {
                bottom: 20%;
                left: 50%;
                width: 100px;
                height: 20px;
                border-radius: 10px;
                transform: translateX(-50%) rotateX(45deg);
              }
            }
          }
        }
      }

      @keyframes rotate3d {
        0% { transform: rotateY(0deg); }
        100% { transform: rotateY(360deg); }
      }

      @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
      }

      .status-indicators {
        display: flex;
        gap: 30px;
        margin-top: 20px;

        .indicator {
          text-align: center;

          .indicator-label {
            font-size: 14px;
            color: #cccccc;
            margin-bottom: 8px;
          }

          .indicator-status {
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;

            &.normal {
              background: rgba(0, 255, 0, 0.2);
              color: #00ff00;
              border: 1px solid #00ff00;
            }

            &.warning {
              background: rgba(255, 255, 0, 0.2);
              color: #ffff00;
              border: 1px solid #ffff00;
            }
          }
        }
      }
    }

    .right-panel {
      width: 350px;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .device-status-section,
      .running-info-section,
      .analysis-section,
      .target-section {
        background: rgba(0, 0, 0, 0.4);
        border: 1px solid #00ffff;
        border-radius: 8px;
        padding: 15px;

        h3 {
          color: #00ffff;
          margin: 0 0 15px 0;
          font-size: 18px;
        }
      }

      .status-card {
        display: flex;
        align-items: center;
        gap: 15px;

        .status-icon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: #00ff00;
          box-shadow: 0 0 15px #00ff00;

          &.healthy {
            background: #00ff00;
            box-shadow: 0 0 15px #00ff00;
          }
        }

        .status-info {
          .status-title {
            font-size: 18px;
            font-weight: bold;
            color: #00ff00;
          }

          .status-time {
            font-size: 12px;
            color: #cccccc;
            margin: 5px 0;
          }

          .status-detail {
            font-size: 14px;
            color: #ffffff;
          }
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;

        .info-item {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 5px;
          font-size: 14px;

          .info-label {
            color: #cccccc;
          }

          .info-value {
            color: #00ffff;
            font-weight: bold;
          }

          .info-unit {
            color: #cccccc;
          }
        }
      }

      .analysis-table {
        .table-header {
          display: grid;
          grid-template-columns: 2fr 1fr 1fr 1fr;
          gap: 10px;
          padding: 8px 0;
          border-bottom: 1px solid #00ffff;
          font-weight: bold;
          color: #00ffff;
          font-size: 12px;
        }

        .table-row {
          display: grid;
          grid-template-columns: 2fr 1fr 1fr 1fr;
          gap: 10px;
          padding: 8px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          font-size: 12px;

          .status-normal {
            color: #00ff00;
          }
        }
      }

      .target-list {
        .target-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          font-size: 14px;

          .target-date {
            color: #cccccc;
          }

          .target-action {
            color: #00ffff;
          }
        }
      }
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: #00ffff;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00cccc;
}
</style>
