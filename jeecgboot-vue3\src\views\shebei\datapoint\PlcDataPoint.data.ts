import { BasicColumn } from '/@/components/Table'
import { FormSchema } from '/@/components/Table'

// 列表页面公共参数
export const columns: BasicColumn[] = [
  {
    title: '点位名称',
    align: 'center',
    dataIndex: 'pointName',
    width: 120,
  },
  {
    title: '点位描述',
    align: 'center',
    dataIndex: 'pointDesc',
    width: 150,
  },
  {
    title: '设备ID',
    align: 'center',
    dataIndex: 'deviceId',
    width: 120,
  },
  {
    title: '数据区域',
    align: 'center',
    dataIndex: 'dataArea',
    width: 80,
  },
  {
    title: 'DB块号',
    align: 'center',
    dataIndex: 'dbNumber',
    width: 80,
  },
  {
    title: '起始地址',
    align: 'center',
    dataIndex: 'startAddress',
    width: 80,
  },
  {
    title: '数据类型',
    align: 'center',
    dataIndex: 'dataType',
    width: 80,
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'unit',
    width: 60,
  },
  {
    title: '监控项目',
    align: 'center',
    dataIndex: 'monitorItem',
    width: 120,
  },
  {
    title: '启用状态',
    align: 'center',
    dataIndex: 'enabled',
    width: 80,
    customRender: ({ text }) => {
      const statusMap = {
        0: { text: '禁用', color: '#ff4d4f' },
        1: { text: '启用', color: '#52c41a' }
      }
      const status = statusMap[text] || { text: '未知', color: '#999' }
      return `<span style="color: ${status.color}">${status.text}</span>`
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    fixed: 'right',
    width: 200,
    slots: { customRender: 'action' },
  },
]

// 表单数据
export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: 'PLC配置',
    field: 'plcId',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => import('./api').then(m => m.getPlcConfigOptions()),
      labelField: 'plcName',
      valueField: 'id',
      placeholder: '请选择PLC配置',
    },
  },
  {
    label: '设备',
    field: 'deviceId',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => import('./api').then(m => m.getDeviceOptions()),
      labelField: 'deviceName',
      valueField: 'id',
      placeholder: '请选择设备',
    },
  },
  {
    label: '点位名称',
    field: 'pointName',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入点位名称，如：voltage',
    },
    dynamicRules: ({ model, schema }) => {
      return [
        { required: true, message: '请输入点位名称!' },
        { max: 100, message: '点位名称不能超过100个字符!' },
      ]
    },
  },
  {
    label: '点位描述',
    field: 'pointDesc',
    component: 'Input',
    componentProps: {
      placeholder: '请输入点位描述，如：电压',
    },
  },
  {
    label: '数据区域',
    field: 'dataArea',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择数据区域',
      options: [
        { label: 'DB-数据块', value: 'DB' },
        { label: 'I-输入', value: 'I' },
        { label: 'Q-输出', value: 'Q' },
        { label: 'M-内存', value: 'M' },
        { label: 'T-定时器', value: 'T' },
        { label: 'C-计数器', value: 'C' },
      ],
    },
  },
  {
    label: 'DB块号',
    field: 'dbNumber',
    component: 'InputNumber',
    ifShow: ({ values }) => values.dataArea === 'DB',
    componentProps: {
      placeholder: '请输入DB块号',
      min: 1,
      max: 65535,
    },
    dynamicRules: ({ values }) => {
      return values.dataArea === 'DB' ? [{ required: true, message: '请输入DB块号!' }] : []
    },
  },
  {
    label: '起始地址',
    field: 'startAddress',
    component: 'InputNumber',
    required: true,
    componentProps: {
      placeholder: '请输入起始地址',
      min: 0,
      max: 65535,
    },
  },
  {
    label: '数据类型',
    field: 'dataType',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择数据类型',
      options: [
        { label: 'BOOL-布尔型', value: 'BOOL' },
        { label: 'BYTE-字节型', value: 'BYTE' },
        { label: 'WORD-字型', value: 'WORD' },
        { label: 'DWORD-双字型', value: 'DWORD' },
        { label: 'INT-整型', value: 'INT' },
        { label: 'DINT-双整型', value: 'DINT' },
        { label: 'REAL-实数型', value: 'REAL' },
        { label: 'STRING-字符串', value: 'STRING' },
      ],
    },
  },
  {
    label: '字符串长度',
    field: 'stringLength',
    component: 'InputNumber',
    ifShow: ({ values }) => values.dataType === 'STRING',
    componentProps: {
      placeholder: '请输入字符串长度',
      min: 1,
      max: 254,
    },
  },
  {
    label: '位偏移',
    field: 'bitOffset',
    component: 'InputNumber',
    ifShow: ({ values }) => values.dataType === 'BOOL',
    componentProps: {
      placeholder: '请输入位偏移(0-7)',
      min: 0,
      max: 7,
    },
  },
  {
    label: '数据单位',
    field: 'unit',
    component: 'Input',
    componentProps: {
      placeholder: '请输入数据单位，如：V、A、℃',
    },
  },
  {
    label: '缩放因子',
    field: 'scaleFactor',
    component: 'InputNumber',
    defaultValue: 1,
    componentProps: {
      placeholder: '请输入缩放因子',
      precision: 6,
    },
  },
  {
    label: '偏移量',
    field: 'offset',
    component: 'InputNumber',
    defaultValue: 0,
    componentProps: {
      placeholder: '请输入偏移量',
      precision: 6,
    },
  },
  {
    label: '监控项目',
    field: 'monitorItem',
    component: 'Input',
    componentProps: {
      placeholder: '请输入监控项目名称，如：电压、电流、温度',
    },
  },
  {
    label: '采集周期(秒)',
    field: 'collectInterval',
    component: 'InputNumber',
    defaultValue: 30,
    componentProps: {
      placeholder: '请输入采集周期',
      min: 1,
      max: 3600,
    },
  },
  {
    label: '最小值',
    field: 'minValue',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入最小值',
      precision: 2,
    },
  },
  {
    label: '最大值',
    field: 'maxValue',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入最大值',
      precision: 2,
    },
  },
  {
    label: '启用状态',
    field: 'enabled',
    component: 'RadioButtonGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    required: true,
  },
]
