-- 设备监控系统初始化SQL脚本

-- 创建设备监控表
DROP TABLE IF EXISTS `device_monitor`;
CREATE TABLE `device_monitor` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `device_code` varchar(50) NOT NULL COMMENT '设备编号',
  `device_name` varchar(100) NOT NULL COMMENT '设备名称',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `status` int(1) DEFAULT '0' COMMENT '设备状态：0-离线，1-在线，2-故障，3-维护',
  `run_status` int(1) DEFAULT '0' COMMENT '运行状态：0-停止，1-运行，2-异常',
  `location` varchar(200) DEFAULT NULL COMMENT '设备位置',
  `load_capacity` varchar(100) DEFAULT NULL COMMENT '负载能力',
  `oil_analysis` varchar(100) DEFAULT NULL COMMENT '油位清洁分析',
  `cooling_life` varchar(100) DEFAULT NULL COMMENT '制冷寿命',
  `intelligent_evaluation` varchar(100) DEFAULT NULL COMMENT '智能评价',
  `voltage` decimal(10,2) DEFAULT NULL COMMENT '电压(V)',
  `current` decimal(10,2) DEFAULT NULL COMMENT '电流(A)',
  `temperature` decimal(5,1) DEFAULT NULL COMMENT '温度(℃)',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_code` (`device_code`),
  KEY `idx_device_type` (`device_type`),
  KEY `idx_status` (`status`),
  KEY `idx_run_status` (`run_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备监控表';

-- 创建设备监控数据表
DROP TABLE IF EXISTS `device_monitor_data`;
CREATE TABLE `device_monitor_data` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `device_id` varchar(36) NOT NULL COMMENT '设备ID',
  `device_code` varchar(50) NOT NULL COMMENT '设备编号',
  `monitor_item` varchar(100) NOT NULL COMMENT '监测项目名称',
  `monitor_value` decimal(15,4) DEFAULT NULL COMMENT '监测值',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `min_value` decimal(15,4) DEFAULT NULL COMMENT '正常范围最小值',
  `max_value` decimal(15,4) DEFAULT NULL COMMENT '正常范围最大值',
  `status` int(1) DEFAULT '0' COMMENT '状态：0-正常，1-预警，2-异常',
  `monitor_time` datetime NOT NULL COMMENT '监测时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_device_code` (`device_code`),
  KEY `idx_monitor_item` (`monitor_item`),
  KEY `idx_monitor_time` (`monitor_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备监控数据表';

-- 插入字典数据
-- 设备类型字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES 
('device_type_dict', '设备类型', 'device_type', '设备类型字典', 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES 
('device_type_1', 'device_type_dict', '变压器', 'transformer', '变压器设备', 1, 1, 'admin', NOW(), 'admin', NOW()),
('device_type_2', 'device_type_dict', '开关柜', 'switchgear', '开关柜设备', 2, 1, 'admin', NOW(), 'admin', NOW()),
('device_type_3', 'device_type_dict', '电容器', 'capacitor', '电容器设备', 3, 1, 'admin', NOW(), 'admin', NOW()),
('device_type_4', 'device_type_dict', '电抗器', 'reactor', '电抗器设备', 4, 1, 'admin', NOW(), 'admin', NOW());

-- 设备状态字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES 
('device_status_dict', '设备状态', 'device_status', '设备状态字典', 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES 
('device_status_0', 'device_status_dict', '离线', '0', '设备离线', 1, 1, 'admin', NOW(), 'admin', NOW()),
('device_status_1', 'device_status_dict', '在线', '1', '设备在线', 2, 1, 'admin', NOW(), 'admin', NOW()),
('device_status_2', 'device_status_dict', '故障', '2', '设备故障', 3, 1, 'admin', NOW(), 'admin', NOW()),
('device_status_3', 'device_status_dict', '维护', '3', '设备维护', 4, 1, 'admin', NOW(), 'admin', NOW());

-- 设备运行状态字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES 
('device_run_status_dict', '设备运行状态', 'device_run_status', '设备运行状态字典', 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES 
('device_run_status_0', 'device_run_status_dict', '停止', '0', '设备停止运行', 1, 1, 'admin', NOW(), 'admin', NOW()),
('device_run_status_1', 'device_run_status_dict', '运行', '1', '设备正常运行', 2, 1, 'admin', NOW(), 'admin', NOW()),
('device_run_status_2', 'device_run_status_dict', '异常', '2', '设备运行异常', 3, 1, 'admin', NOW(), 'admin', NOW());

-- 插入示例设备数据
INSERT INTO `device_monitor` (`id`, `device_code`, `device_name`, `device_type`, `status`, `run_status`, `location`, `load_capacity`, `oil_analysis`, `cooling_life`, `intelligent_evaluation`, `voltage`, `current`, `temperature`, `last_update_time`, `create_by`, `create_time`) VALUES 
('device_001', 'DEV001', '主变压器A', 'transformer', 1, 1, '变电站A区', '正常', '正常', '5年', '正常', 500.00, 120.50, 35.2, NOW(), 'admin', NOW()),
('device_002', 'DEV002', '主变压器B', 'transformer', 1, 1, '变电站B区', '正常', '正常', '3年', '正常', 500.00, 115.30, 36.1, NOW(), 'admin', NOW()),
('device_003', 'DEV003', '开关柜1号', 'switchgear', 1, 1, '变电站C区', '正常', '正常', '8年', '正常', 500.00, 0.00, 32.5, NOW(), 'admin', NOW()),
('device_004', 'DEV004', '开关柜2号', 'switchgear', 2, 2, '变电站D区', '异常', '正常', '2年', '异常', 480.00, 0.00, 45.8, NOW(), 'admin', NOW()),
('device_005', 'DEV005', '电容器组A', 'capacitor', 1, 1, '变电站E区', '正常', '正常', '6年', '正常', 500.00, 85.20, 28.9, NOW(), 'admin', NOW());

-- 插入示例监控数据
INSERT INTO `device_monitor_data` (`id`, `device_id`, `device_code`, `monitor_item`, `monitor_value`, `unit`, `min_value`, `max_value`, `status`, `monitor_time`, `create_time`) VALUES 
('data_001', 'device_001', 'DEV001', '负载能力', 85.5, '%', 0, 100, 0, NOW(), NOW()),
('data_002', 'device_001', 'DEV001', '油位清洁', 95.2, '%', 80, 100, 0, NOW(), NOW()),
('data_003', 'device_001', 'DEV001', '制冷效率', 92.8, '%', 85, 100, 0, NOW(), NOW()),
('data_004', 'device_002', 'DEV002', '负载能力', 78.3, '%', 0, 100, 0, NOW(), NOW()),
('data_005', 'device_002', 'DEV002', '油位清洁', 88.7, '%', 80, 100, 0, NOW(), NOW()),
('data_006', 'device_003', 'DEV003', '负载能力', 65.4, '%', 0, 100, 0, NOW(), NOW()),
('data_007', 'device_004', 'DEV004', '负载能力', 45.2, '%', 0, 100, 1, NOW(), NOW()),
('data_008', 'device_005', 'DEV005', '负载能力', 92.1, '%', 0, 100, 0, NOW(), NOW());

-- 添加菜单权限
-- 主菜单
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES 
('shebei_monitor_menu', NULL, '设备监控', '/shebei', 'layouts/default/index', NULL, '/shebei/monitor/list', 0, NULL, '1', 1.00, 0, 'ant-design:monitor-outlined', 1, 0, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);

-- 设备监控管理菜单
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES 
('shebei_monitor_list', 'shebei_monitor_menu', '设备监控管理', '/shebei/monitor/list', 'shebei/monitor/DeviceMonitorList', NULL, NULL, 1, NULL, '1', 1.00, 0, 'ant-design:unordered-list-outlined', 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);

-- 监控大屏菜单
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES 
('shebei_monitor_dashboard', 'shebei_monitor_menu', '监控大屏', '/shebei/monitor/dashboard', 'shebei/monitor/DeviceMonitorDashboard', NULL, NULL, 1, NULL, '1', 2.00, 0, 'ant-design:dashboard-outlined', 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);

-- 按钮权限
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES 
('shebei_monitor_add', 'shebei_monitor_list', '添加', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:add', '1', 1.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('shebei_monitor_edit', 'shebei_monitor_list', '编辑', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:edit', '1', 2.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('shebei_monitor_delete', 'shebei_monitor_list', '删除', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:delete', '1', 3.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('shebei_monitor_query', 'shebei_monitor_list', '查询', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:query', '1', 4.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('shebei_monitor_export', 'shebei_monitor_list', '导出', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:export', '1', 5.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('shebei_monitor_import', 'shebei_monitor_list', '导入', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:import', '1', 6.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);
