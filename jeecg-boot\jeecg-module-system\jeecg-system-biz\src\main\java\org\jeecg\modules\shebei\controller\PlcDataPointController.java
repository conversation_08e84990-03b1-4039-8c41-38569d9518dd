package org.jeecg.modules.shebei.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.shebei.entity.PlcDataPoint;
import org.jeecg.modules.shebei.service.IPlcDataPointService;
import org.jeecg.modules.shebei.service.IPlcDataCollectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;

/**
 * PLC数据点位配置Controller
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@RestController
@RequestMapping("/shebei/plcDataPoint")
@Slf4j
public class PlcDataPointController {
    
    @Autowired
    private IPlcDataPointService plcDataPointService;
    
    @Autowired
    private IPlcDataCollectionService plcDataCollectionService;
    
    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    public Result<IPage<PlcDataPoint>> queryPageList(PlcDataPoint plcDataPoint,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        QueryWrapper<PlcDataPoint> queryWrapper = QueryGenerator.initQueryWrapper(plcDataPoint, req.getParameterMap());
        Page<PlcDataPoint> page = new Page<PlcDataPoint>(pageNo, pageSize);
        IPage<PlcDataPoint> pageList = plcDataPointService.page(page, queryWrapper);
        return Result.OK(pageList);
    }
    
    /**
     * 添加
     */
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody PlcDataPoint plcDataPoint) {
        plcDataPoint.setCreateTime(new Date());
        plcDataPointService.save(plcDataPoint);
        return Result.OK("添加成功！");
    }
    
    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody PlcDataPoint plcDataPoint) {
        plcDataPoint.setUpdateTime(new Date());
        plcDataPointService.updateById(plcDataPoint);
        return Result.OK("编辑成功!");
    }
    
    /**
     * 通过id删除
     */
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        plcDataPointService.removeById(id);
        return Result.OK("删除成功!");
    }
    
    /**
     * 批量删除
     */
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.plcDataPointService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }
    
    /**
     * 通过id查询
     */
    @GetMapping(value = "/queryById")
    public Result<PlcDataPoint> queryById(@RequestParam(name = "id", required = true) String id) {
        PlcDataPoint plcDataPoint = plcDataPointService.getById(id);
        if (plcDataPoint == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(plcDataPoint);
    }
    
    /**
     * 测试数据点位读取
     */
    @PostMapping(value = "/testRead")
    public Result<Object> testRead(@RequestParam(name = "id", required = true) String id) {
        try {
            PlcDataPoint dataPoint = plcDataPointService.getById(id);
            if (dataPoint == null) {
                return Result.error("数据点位不存在");
            }
            
            Object value = plcDataCollectionService.collectSingleDataPoint(dataPoint);
            if (value != null) {
                return Result.OK(value, "读取成功，值为：" + value);
            } else {
                return Result.error("读取失败，请检查PLC连接和点位配置");
            }
        } catch (Exception e) {
            log.error("测试数据点位读取异常", e);
            return Result.error("读取异常：" + e.getMessage());
        }
    }
    
    /**
     * 启用/禁用数据点位
     */
    @PostMapping(value = "/toggleEnabled")
    public Result<String> toggleEnabled(@RequestParam(name = "id", required = true) String id,
                                        @RequestParam(name = "enabled", required = true) Integer enabled) {
        try {
            PlcDataPoint dataPoint = new PlcDataPoint();
            dataPoint.setId(id);
            dataPoint.setEnabled(enabled);
            dataPoint.setUpdateTime(new Date());
            plcDataPointService.updateById(dataPoint);
            
            String status = enabled == 1 ? "启用" : "禁用";
            return Result.OK("数据点位" + status + "成功");
        } catch (Exception e) {
            log.error("切换数据点位状态异常", e);
            return Result.error("操作失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据设备ID获取数据点位列表
     */
    @GetMapping(value = "/listByDevice")
    public Result<java.util.List<PlcDataPoint>> listByDevice(@RequestParam(name = "deviceId", required = true) String deviceId) {
        java.util.List<PlcDataPoint> dataPoints = plcDataCollectionService.getDeviceDataPoints(deviceId);
        return Result.OK(dataPoints);
    }
}
