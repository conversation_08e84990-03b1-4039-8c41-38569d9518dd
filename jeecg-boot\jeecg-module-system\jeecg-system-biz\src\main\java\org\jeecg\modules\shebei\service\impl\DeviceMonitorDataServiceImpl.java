package org.jeecg.modules.shebei.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.shebei.entity.DeviceMonitorData;
import org.jeecg.modules.shebei.mapper.DeviceMonitorDataMapper;
import org.jeecg.modules.shebei.service.IDeviceMonitorDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 设备监控数据Service实现类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
public class DeviceMonitorDataServiceImpl extends ServiceImpl<DeviceMonitorDataMapper, DeviceMonitorData> implements IDeviceMonitorDataService {
    
    @Autowired
    private DeviceMonitorDataMapper deviceMonitorDataMapper;
    
    @Override
    public List<DeviceMonitorData> getLatestDataByDeviceId(String deviceId) {
        return deviceMonitorDataMapper.getLatestDataByDeviceId(deviceId);
    }
    
    @Override
    public List<DeviceMonitorData> getHistoryDataByDeviceId(String deviceId, Integer hours) {
        return deviceMonitorDataMapper.getHistoryDataByDeviceId(deviceId, hours);
    }
    
    @Override
    public List<Map<String, Object>> getMonitorDataAnalysis() {
        return deviceMonitorDataMapper.getMonitorDataAnalysis();
    }
    
    @Override
    public List<Map<String, Object>> getDeviceTrendData(String deviceId, Integer hours) {
        return deviceMonitorDataMapper.getDeviceTrendData(deviceId, hours);
    }
    
    @Override
    public boolean batchSaveMonitorData(List<DeviceMonitorData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return true;
        }
        return this.saveBatch(dataList);
    }
}
