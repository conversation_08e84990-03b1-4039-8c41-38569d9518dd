package org.jeecg.modules.shebei.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.shebei.entity.DeviceMonitor;
import org.jeecg.modules.shebei.entity.DeviceMonitorData;
import org.jeecg.modules.shebei.service.IDeviceMonitorDataService;
import org.jeecg.modules.shebei.service.IDeviceMonitorService;
import org.jeecg.modules.shebei.vo.DeviceMonitorDashboardVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备监控Controller
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Api(tags = "设备监控")
@RestController
@RequestMapping("/shebei/deviceMonitor")
@Slf4j
public class DeviceMonitorController {
    
    @Autowired
    private IDeviceMonitorService deviceMonitorService;
    
    @Autowired
    private IDeviceMonitorDataService deviceMonitorDataService;
    
    /**
     * 分页列表查询
     */
    @ApiOperation(value = "设备监控-分页列表查询", notes = "设备监控-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<DeviceMonitor>> queryPageList(DeviceMonitor deviceMonitor,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        QueryWrapper<DeviceMonitor> queryWrapper = QueryGenerator.initQueryWrapper(deviceMonitor, req.getParameterMap());
        Page<DeviceMonitor> page = new Page<DeviceMonitor>(pageNo, pageSize);
        IPage<DeviceMonitor> pageList = deviceMonitorService.page(page, queryWrapper);
        return Result.OK(pageList);
    }
    
    /**
     * 添加
     */
    @ApiOperation(value = "设备监控-添加", notes = "设备监控-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody DeviceMonitor deviceMonitor) {
        deviceMonitor.setCreateTime(new Date());
        deviceMonitor.setLastUpdateTime(new Date());
        deviceMonitorService.save(deviceMonitor);
        return Result.OK("添加成功！");
    }
    
    /**
     * 编辑
     */
    @ApiOperation(value = "设备监控-编辑", notes = "设备监控-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody DeviceMonitor deviceMonitor) {
        deviceMonitor.setUpdateTime(new Date());
        deviceMonitorService.updateById(deviceMonitor);
        return Result.OK("编辑成功!");
    }
    
    /**
     * 通过id删除
     */
    @ApiOperation(value = "设备监控-通过id删除", notes = "设备监控-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        deviceMonitorService.removeById(id);
        return Result.OK("删除成功!");
    }
    
    /**
     * 批量删除
     */
    @ApiOperation(value = "设备监控-批量删除", notes = "设备监控-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.deviceMonitorService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }
    
    /**
     * 通过id查询
     */
    @ApiOperation(value = "设备监控-通过id查询", notes = "设备监控-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<DeviceMonitor> queryById(@RequestParam(name = "id", required = true) String id) {
        DeviceMonitor deviceMonitor = deviceMonitorService.getById(id);
        if (deviceMonitor == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(deviceMonitor);
    }
    
    /**
     * 获取设备监控大屏数据
     */
    @ApiOperation(value = "获取设备监控大屏数据", notes = "获取设备监控大屏数据")
    @GetMapping(value = "/dashboard")
    public Result<DeviceMonitorDashboardVO> getDashboardData() {
        DeviceMonitorDashboardVO dashboardData = deviceMonitorService.getDashboardData();
        return Result.OK(dashboardData);
    }
    
    /**
     * 获取设备最新监控数据
     */
    @ApiOperation(value = "获取设备最新监控数据", notes = "获取设备最新监控数据")
    @GetMapping(value = "/latestData")
    public Result<List<DeviceMonitorData>> getLatestData(@RequestParam(name = "deviceId", required = true) String deviceId) {
        List<DeviceMonitorData> dataList = deviceMonitorDataService.getLatestDataByDeviceId(deviceId);
        return Result.OK(dataList);
    }
    
    /**
     * 获取设备历史监控数据
     */
    @ApiOperation(value = "获取设备历史监控数据", notes = "获取设备历史监控数据")
    @GetMapping(value = "/historyData")
    public Result<List<DeviceMonitorData>> getHistoryData(@RequestParam(name = "deviceId", required = true) String deviceId,
                                                          @RequestParam(name = "hours", defaultValue = "24") Integer hours) {
        List<DeviceMonitorData> dataList = deviceMonitorDataService.getHistoryDataByDeviceId(deviceId, hours);
        return Result.OK(dataList);
    }
    
    /**
     * 获取设备监控趋势数据
     */
    @ApiOperation(value = "获取设备监控趋势数据", notes = "获取设备监控趋势数据")
    @GetMapping(value = "/trendData")
    public Result<List<Map<String, Object>>> getTrendData(@RequestParam(name = "deviceId", required = true) String deviceId,
                                                          @RequestParam(name = "hours", defaultValue = "24") Integer hours) {
        List<Map<String, Object>> trendData = deviceMonitorDataService.getDeviceTrendData(deviceId, hours);
        return Result.OK(trendData);
    }
    
    /**
     * 更新设备状态
     */
    @ApiOperation(value = "更新设备状态", notes = "更新设备状态")
    @PostMapping(value = "/updateStatus")
    public Result<String> updateStatus(@RequestParam(name = "deviceId", required = true) String deviceId,
                                       @RequestParam(name = "status", required = true) Integer status,
                                       @RequestParam(name = "runStatus", required = true) Integer runStatus) {
        boolean success = deviceMonitorService.updateDeviceStatus(deviceId, status, runStatus);
        if (success) {
            return Result.OK("状态更新成功!");
        } else {
            return Result.error("状态更新失败!");
        }
    }
}
