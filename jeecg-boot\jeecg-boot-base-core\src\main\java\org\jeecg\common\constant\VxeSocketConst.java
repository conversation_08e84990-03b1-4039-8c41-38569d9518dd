package org.jeecg.common.constant;

/**
 * VXESocket 常量
 * @author: jeecg-boot
 */
public class VxeSocketConst {

    /**
     * 消息类型
     */
    public static final String TYPE = "type";
    /**
     * 消息数据
     */
    public static final String DATA = "data";

    /**
     * 消息类型：心跳检测
     */
    public static final String TYPE_HB = "heart_beat";
    /**
     * 消息类型：通用数据传递
     */
    public static final String TYPE_CSD = "common_send_date";
    /**
     * 消息类型：更新vxe table数据
     */
    public static final String TYPE_UVT = "update_vxe_table";

}
