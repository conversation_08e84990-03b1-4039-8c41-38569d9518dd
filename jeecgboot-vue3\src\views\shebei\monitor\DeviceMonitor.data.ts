import { BasicColumn } from '/@/components/Table'
import { FormSchema } from '/@/components/Table'
import { rules } from '/@/utils/helper/validator'
import { render } from '/@/utils/common/renderUtils'

// 列表页面公共参数
export const columns: BasicColumn[] = [
  {
    title: '设备编号',
    align: 'center',
    dataIndex: 'deviceCode',
    width: 120,
  },
  {
    title: '设备名称',
    align: 'center',
    dataIndex: 'deviceName',
    width: 150,
  },
  {
    title: '设备类型',
    align: 'center',
    dataIndex: 'deviceType',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(text, 'device_type')
    },
  },
  {
    title: '设备状态',
    align: 'center',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        0: { text: '离线', color: '#999' },
        1: { text: '在线', color: '#52c41a' },
        2: { text: '故障', color: '#ff4d4f' },
        3: { text: '维护', color: '#faad14' }
      }
      const status = statusMap[text] || { text: '未知', color: '#999' }
      return `<span style="color: ${status.color}">${status.text}</span>`
    },
  },
  {
    title: '运行状态',
    align: 'center',
    dataIndex: 'runStatus',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        0: { text: '停止', color: '#999' },
        1: { text: '运行', color: '#52c41a' },
        2: { text: '异常', color: '#ff4d4f' }
      }
      const status = statusMap[text] || { text: '未知', color: '#999' }
      return `<span style="color: ${status.color}">${status.text}</span>`
    },
  },
  {
    title: '设备位置',
    align: 'center',
    dataIndex: 'location',
    width: 150,
  },
  {
    title: '电压(V)',
    align: 'center',
    dataIndex: 'voltage',
    width: 100,
  },
  {
    title: '电流(A)',
    align: 'center',
    dataIndex: 'current',
    width: 100,
  },
  {
    title: '温度(℃)',
    align: 'center',
    dataIndex: 'temperature',
    width: 100,
  },
  {
    title: '最后更新时间',
    align: 'center',
    dataIndex: 'lastUpdateTime',
    width: 160,
  },
]

// 查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '设备编号',
    field: 'deviceCode',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '设备名称',
    field: 'deviceName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '设备类型',
    field: 'deviceType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'device_type',
      placeholder: '请选择设备类型',
    },
    colProps: { span: 6 },
  },
  {
    label: '设备状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'device_status',
      placeholder: '请选择设备状态',
    },
    colProps: { span: 6 },
  },
]

// 表单数据
export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '设备编号',
    field: 'deviceCode',
    component: 'Input',
    required: true,
    dynamicRules: ({ model, schema }) => {
      return [
        { required: true, message: '请输入设备编号!' },
        { max: 50, message: '设备编号不能超过50个字符!' },
      ]
    },
  },
  {
    label: '设备名称',
    field: 'deviceName',
    component: 'Input',
    required: true,
    dynamicRules: ({ model, schema }) => {
      return [
        { required: true, message: '请输入设备名称!' },
        { max: 100, message: '设备名称不能超过100个字符!' },
      ]
    },
  },
  {
    label: '设备类型',
    field: 'deviceType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'device_type',
      placeholder: '请选择设备类型',
    },
    required: true,
  },
  {
    label: '设备状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'device_status',
      placeholder: '请选择设备状态',
    },
    required: true,
  },
  {
    label: '运行状态',
    field: 'runStatus',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'device_run_status',
      placeholder: '请选择运行状态',
    },
    required: true,
  },
  {
    label: '设备位置',
    field: 'location',
    component: 'Input',
    componentProps: {
      placeholder: '请输入设备位置',
    },
  },
  {
    label: '负载能力',
    field: 'loadCapacity',
    component: 'Input',
    componentProps: {
      placeholder: '请输入负载能力',
    },
  },
  {
    label: '油位清洁分析',
    field: 'oilAnalysis',
    component: 'Input',
    componentProps: {
      placeholder: '请输入油位清洁分析',
    },
  },
  {
    label: '制冷寿命',
    field: 'coolingLife',
    component: 'Input',
    componentProps: {
      placeholder: '请输入制冷寿命',
    },
  },
  {
    label: '智能评价',
    field: 'intelligentEvaluation',
    component: 'Input',
    componentProps: {
      placeholder: '请输入智能评价',
    },
  },
  {
    label: '电压(V)',
    field: 'voltage',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入电压',
      precision: 2,
    },
  },
  {
    label: '电流(A)',
    field: 'current',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入电流',
      precision: 2,
    },
  },
  {
    label: '温度(℃)',
    field: 'temperature',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入温度',
      precision: 1,
    },
  },
]

// 高级查询数据
export const superQuerySchema = {
  deviceCode: { title: '设备编号', order: 0, view: 'text', type: 'string' },
  deviceName: { title: '设备名称', order: 1, view: 'text', type: 'string' },
  deviceType: { title: '设备类型', order: 2, view: 'list', type: 'string', dictCode: 'device_type' },
  status: { title: '设备状态', order: 3, view: 'list', type: 'string', dictCode: 'device_status' },
  runStatus: { title: '运行状态', order: 4, view: 'list', type: 'string', dictCode: 'device_run_status' },
  location: { title: '设备位置', order: 5, view: 'text', type: 'string' },
  voltage: { title: '电压', order: 6, view: 'number', type: 'number' },
  current: { title: '电流', order: 7, view: 'number', type: 'number' },
  temperature: { title: '温度', order: 8, view: 'number', type: 'number' },
  lastUpdateTime: { title: '最后更新时间', order: 9, view: 'datetime', type: 'Date' },
}
