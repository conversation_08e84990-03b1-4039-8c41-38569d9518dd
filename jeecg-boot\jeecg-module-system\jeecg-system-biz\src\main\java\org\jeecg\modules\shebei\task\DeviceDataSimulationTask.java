package org.jeecg.modules.shebei.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.shebei.entity.DeviceMonitor;
import org.jeecg.modules.shebei.entity.DeviceMonitorData;
import org.jeecg.modules.shebei.service.IDeviceMonitorDataService;
import org.jeecg.modules.shebei.service.IDeviceMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * 设备数据模拟任务
 * 用于模拟设备实时数据更新
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Component
@Slf4j
public class DeviceDataSimulationTask {
    
    @Autowired
    private IDeviceMonitorService deviceMonitorService;
    
    @Autowired
    private IDeviceMonitorDataService deviceMonitorDataService;
    
    private final Random random = new Random();
    
    /**
     * 每分钟执行一次，模拟设备数据更新
     */
    @Scheduled(fixedRate = 60000) // 60秒执行一次
    public void simulateDeviceData() {
        try {
            log.info("开始模拟设备数据更新...");
            
            // 获取所有在线设备
            List<DeviceMonitor> onlineDevices = deviceMonitorService.getOnlineDevices(100);
            
            if (onlineDevices.isEmpty()) {
                log.warn("没有在线设备，跳过数据模拟");
                return;
            }
            
            List<DeviceMonitorData> dataList = new ArrayList<>();
            Date now = new Date();
            
            for (DeviceMonitor device : onlineDevices) {
                // 模拟不同类型设备的监控数据
                switch (device.getDeviceType()) {
                    case "transformer":
                        dataList.addAll(simulateTransformerData(device, now));
                        break;
                    case "switchgear":
                        dataList.addAll(simulateSwitchgearData(device, now));
                        break;
                    case "capacitor":
                        dataList.addAll(simulateCapacitorData(device, now));
                        break;
                    case "reactor":
                        dataList.addAll(simulateReactorData(device, now));
                        break;
                    default:
                        dataList.addAll(simulateDefaultData(device, now));
                        break;
                }
                
                // 更新设备基础参数
                updateDeviceParameters(device);
            }
            
            // 批量保存监控数据
            if (!dataList.isEmpty()) {
                deviceMonitorDataService.batchSaveMonitorData(dataList);
                log.info("成功模拟 {} 条设备监控数据", dataList.size());
            }
            
        } catch (Exception e) {
            log.error("模拟设备数据更新失败", e);
        }
    }
    
    /**
     * 模拟变压器数据
     */
    private List<DeviceMonitorData> simulateTransformerData(DeviceMonitor device, Date time) {
        List<DeviceMonitorData> dataList = new ArrayList<>();
        
        // 负载能力 (70-95%)
        dataList.add(createMonitorData(device, "负载能力", 
                generateRandomValue(70, 95), "%", BigDecimal.ZERO, new BigDecimal(100), 0, time));
        
        // 油位清洁 (85-98%)
        dataList.add(createMonitorData(device, "油位清洁", 
                generateRandomValue(85, 98), "%", new BigDecimal(80), new BigDecimal(100), 0, time));
        
        // 制冷效率 (88-96%)
        dataList.add(createMonitorData(device, "制冷效率", 
                generateRandomValue(88, 96), "%", new BigDecimal(85), new BigDecimal(100), 0, time));
        
        // 绝缘电阻 (1200-1800 MΩ)
        dataList.add(createMonitorData(device, "绝缘电阻", 
                generateRandomValue(1200, 1800), "MΩ", new BigDecimal(1000), new BigDecimal(2000), 0, time));
        
        return dataList;
    }
    
    /**
     * 模拟开关柜数据
     */
    private List<DeviceMonitorData> simulateSwitchgearData(DeviceMonitor device, Date time) {
        List<DeviceMonitorData> dataList = new ArrayList<>();
        
        // 负载能力 (60-90%)
        dataList.add(createMonitorData(device, "负载能力", 
                generateRandomValue(60, 90), "%", BigDecimal.ZERO, new BigDecimal(100), 0, time));
        
        // 开关次数 (随机增加)
        dataList.add(createMonitorData(device, "开关次数", 
                generateRandomValue(1000, 9000), "次", BigDecimal.ZERO, new BigDecimal(10000), 0, time));
        
        // 绝缘电阻 (1400-1900 MΩ)
        dataList.add(createMonitorData(device, "绝缘电阻", 
                generateRandomValue(1400, 1900), "MΩ", new BigDecimal(1000), new BigDecimal(2000), 0, time));
        
        // 接触电阻 (30-80 μΩ)
        dataList.add(createMonitorData(device, "接触电阻", 
                generateRandomValue(30, 80), "μΩ", BigDecimal.ZERO, new BigDecimal(100), 0, time));
        
        return dataList;
    }
    
    /**
     * 模拟电容器数据
     */
    private List<DeviceMonitorData> simulateCapacitorData(DeviceMonitor device, Date time) {
        List<DeviceMonitorData> dataList = new ArrayList<>();
        
        // 负载能力 (80-95%)
        dataList.add(createMonitorData(device, "负载能力", 
                generateRandomValue(80, 95), "%", BigDecimal.ZERO, new BigDecimal(100), 0, time));
        
        // 电容值 (96-104%)
        dataList.add(createMonitorData(device, "电容值", 
                generateRandomValue(96, 104), "%", new BigDecimal(95), new BigDecimal(105), 0, time));
        
        // 绝缘电阻 (1300-1700 MΩ)
        dataList.add(createMonitorData(device, "绝缘电阻", 
                generateRandomValue(1300, 1700), "MΩ", new BigDecimal(1000), new BigDecimal(2000), 0, time));
        
        // 介质损耗 (0.1-0.4%)
        dataList.add(createMonitorData(device, "介质损耗", 
                generateRandomValue(0.1, 0.4), "%", BigDecimal.ZERO, new BigDecimal(0.5), 0, time));
        
        return dataList;
    }
    
    /**
     * 模拟电抗器数据
     */
    private List<DeviceMonitorData> simulateReactorData(DeviceMonitor device, Date time) {
        List<DeviceMonitorData> dataList = new ArrayList<>();
        
        // 负载能力 (70-85%)
        dataList.add(createMonitorData(device, "负载能力", 
                generateRandomValue(70, 85), "%", BigDecimal.ZERO, new BigDecimal(100), 0, time));
        
        // 电感值 (97-103%)
        dataList.add(createMonitorData(device, "电感值", 
                generateRandomValue(97, 103), "%", new BigDecimal(95), new BigDecimal(105), 0, time));
        
        // 绝缘电阻 (1400-1800 MΩ)
        dataList.add(createMonitorData(device, "绝缘电阻", 
                generateRandomValue(1400, 1800), "MΩ", new BigDecimal(1000), new BigDecimal(2000), 0, time));
        
        // 损耗因数 (0.1-0.25%)
        dataList.add(createMonitorData(device, "损耗因数", 
                generateRandomValue(0.1, 0.25), "%", BigDecimal.ZERO, new BigDecimal(0.3), 0, time));
        
        return dataList;
    }
    
    /**
     * 模拟默认数据
     */
    private List<DeviceMonitorData> simulateDefaultData(DeviceMonitor device, Date time) {
        List<DeviceMonitorData> dataList = new ArrayList<>();
        
        // 负载能力
        dataList.add(createMonitorData(device, "负载能力", 
                generateRandomValue(60, 90), "%", BigDecimal.ZERO, new BigDecimal(100), 0, time));
        
        return dataList;
    }
    
    /**
     * 更新设备基础参数
     */
    private void updateDeviceParameters(DeviceMonitor device) {
        // 模拟电压波动 (490-510V)
        BigDecimal voltage = generateRandomValue(490, 510);
        device.setVoltage(voltage);
        
        // 模拟电流变化 (根据负载能力计算)
        BigDecimal current = generateRandomValue(80, 130);
        device.setCurrent(current);
        
        // 模拟温度变化 (25-45℃)
        BigDecimal temperature = generateRandomValue(25, 45);
        device.setTemperature(temperature);
        
        // 更新最后更新时间
        device.setLastUpdateTime(new Date());
        
        // 保存设备信息
        deviceMonitorService.updateById(device);
    }
    
    /**
     * 创建监控数据对象
     */
    private DeviceMonitorData createMonitorData(DeviceMonitor device, String monitorItem, 
                                                BigDecimal value, String unit, 
                                                BigDecimal minValue, BigDecimal maxValue, 
                                                Integer status, Date time) {
        DeviceMonitorData data = new DeviceMonitorData();
        data.setDeviceId(device.getId());
        data.setDeviceCode(device.getDeviceCode());
        data.setMonitorItem(monitorItem);
        data.setMonitorValue(value);
        data.setUnit(unit);
        data.setMinValue(minValue);
        data.setMaxValue(maxValue);
        data.setStatus(status);
        data.setMonitorTime(time);
        data.setCreateTime(time);
        return data;
    }
    
    /**
     * 生成指定范围内的随机数值
     */
    private BigDecimal generateRandomValue(double min, double max) {
        double value = min + (max - min) * random.nextDouble();
        return new BigDecimal(value).setScale(2, RoundingMode.HALF_UP);
    }
}
