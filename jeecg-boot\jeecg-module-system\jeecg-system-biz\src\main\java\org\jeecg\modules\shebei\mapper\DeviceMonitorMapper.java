package org.jeecg.modules.shebei.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.shebei.entity.DeviceMonitor;
import org.jeecg.modules.shebei.vo.DeviceStatusStatVO;

import java.util.List;
import java.util.Map;

/**
 * 设备监控Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Mapper
public interface DeviceMonitorMapper extends BaseMapper<DeviceMonitor> {
    
    /**
     * 获取设备状态统计
     */
    @Select("SELECT status, COUNT(*) as count FROM device_monitor GROUP BY status")
    List<DeviceStatusStatVO> getDeviceStatusStat();
    
    /**
     * 获取设备运行状态统计
     */
    @Select("SELECT run_status, COUNT(*) as count FROM device_monitor GROUP BY run_status")
    List<Map<String, Object>> getDeviceRunStatusStat();
    
    /**
     * 获取设备类型统计
     */
    @Select("SELECT device_type, COUNT(*) as count FROM device_monitor GROUP BY device_type")
    List<Map<String, Object>> getDeviceTypeStat();
    
    /**
     * 获取在线设备列表
     */
    @Select("SELECT * FROM device_monitor WHERE status = 1 ORDER BY last_update_time DESC LIMIT #{limit}")
    List<DeviceMonitor> getOnlineDevices(@Param("limit") Integer limit);
    
    /**
     * 获取异常设备列表
     */
    @Select("SELECT * FROM device_monitor WHERE status IN (2, 3) OR run_status = 2 ORDER BY last_update_time DESC")
    List<DeviceMonitor> getAbnormalDevices();
}
