package org.jeecg.modules.shebei.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备监控实体类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@TableName("device_monitor")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class DeviceMonitor implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 设备编号
     */
    @Excel(name = "设备编号", width = 15)
    private String deviceCode;
    
    /**
     * 设备名称
     */
    @Excel(name = "设备名称", width = 20)
    private String deviceName;
    
    /**
     * 设备类型
     */
    @Excel(name = "设备类型", width = 15)
    @Dict(dicCode = "device_type")
    private String deviceType;
    
    /**
     * 设备状态：0-离线，1-在线，2-故障，3-维护
     */
    @Excel(name = "设备状态", width = 15)
    @Dict(dicCode = "device_status")
    private Integer status;
    
    /**
     * 运行状态：0-停止，1-运行，2-异常
     */
    @Excel(name = "运行状态", width = 15)
    @Dict(dicCode = "device_run_status")
    private Integer runStatus;
    
    /**
     * 设备位置
     */
    @Excel(name = "设备位置", width = 20)
    private String location;
    
    /**
     * 负载能力
     */
    @Excel(name = "负载能力", width = 15)
    private String loadCapacity;
    
    /**
     * 油位清洁分析
     */
    @Excel(name = "油位清洁分析", width = 15)
    private String oilAnalysis;
    
    /**
     * 制冷寿命
     */
    @Excel(name = "制冷寿命", width = 15)
    private String coolingLife;
    
    /**
     * 智能评价
     */
    @Excel(name = "智能评价", width = 15)
    private String intelligentEvaluation;
    
    /**
     * 电压(V)
     */
    @Excel(name = "电压", width = 15)
    private BigDecimal voltage;
    
    /**
     * 电流(A)
     */
    @Excel(name = "电流", width = 15)
    private BigDecimal current;
    
    /**
     * 温度(℃)
     */
    @Excel(name = "温度", width = 15)
    private BigDecimal temperature;
    
    /**
     * 最后更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
