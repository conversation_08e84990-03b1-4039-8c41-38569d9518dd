# S7Connector依赖配置

## 📦 Maven依赖

请在 `jeecg-boot/jeecg-module-system/jeecg-system-biz/pom.xml` 文件中添加以下依赖：

```xml
<dependencies>
    <!-- S7Connector库 -->
    <dependency>
        <groupId>com.github.s7connector</groupId>
        <artifactId>s7connector</artifactId>
        <version>2.1</version>
    </dependency>
</dependencies>
```

## 🔧 代码修改完成

我已经将所有代码修改为使用s7connector库：

### 1. S7PlcUtil.java
- ✅ 使用 `S7Connector` 替代自定义客户端
- ✅ 使用 `S7ConnectorFactory` 创建连接
- ✅ 使用 `DaveArea` 枚举指定数据区域
- ✅ 实现连接池管理
- ✅ 完整的数据类型解析

### 2. PlcDataCollectionServiceImpl.java
- ✅ 调用 `S7PlcUtil.readDataPoint()` 方法
- ✅ 移除所有自定义S7协议代码
- ✅ 简化的API调用

## 🚀 使用方法

### 测试PLC连接
```java
PlcConfig plcConfig = new PlcConfig();
plcConfig.setIpAddress("*************");
plcConfig.setPort(102);
plcConfig.setRack(0);
plcConfig.setSlot(1);

boolean connected = S7PlcUtil.testConnection(plcConfig);
```

### 读取数据点位
```java
Object value = S7PlcUtil.readDataPoint(plcConfig, dataPoint);
```

## 📋 支持的功能

### 数据区域映射
| 配置值 | S7Connector枚举 | 说明 |
|-------|----------------|------|
| DB | DaveArea.DB | 数据块 |
| I | DaveArea.INPUTS | 输入区域 |
| Q | DaveArea.OUTPUTS | 输出区域 |
| M | DaveArea.FLAGS | 内存区域 |

### 数据类型支持
- ✅ BOOL - 布尔值
- ✅ BYTE - 字节
- ✅ WORD - 字
- ✅ DWORD - 双字
- ✅ INT - 整数
- ✅ DINT - 双整数
- ✅ REAL - 浮点数
- ✅ STRING - 字符串

## 🔍 配置示例

### PLC配置
```sql
INSERT INTO plc_config (
    id, plc_name, ip_address, port, rack, slot, enabled
) VALUES (
    'plc_001', '变电站PLC', '*************', 102, 0, 1, 1
);
```

### 数据点位配置
```sql
-- 读取DB1.DBD0的电压数据
INSERT INTO plc_data_point (
    id, plc_id, device_id, point_name, point_desc,
    data_area, db_number, start_address, data_type,
    unit, monitor_item, enabled
) VALUES (
    'point_001', 'plc_001', 'device_001', 'voltage', '电压',
    'DB', 1, 0, 'REAL',
    'V', '电压', 1
);
```

## ⚠️ 注意事项

1. **添加依赖**：确保在pom.xml中添加了s7connector依赖
2. **网络连接**：确保服务器能访问PLC的IP地址
3. **PLC配置**：确保PLC启用了S7通信功能
4. **数据块**：确保PLC中存在相应的数据块

## 🛠️ 测试方法

1. **添加依赖后重新编译项目**
2. **在PLC配置管理页面测试连接**
3. **在数据点位配置页面测试读取**
4. **查看系统日志确认无错误**

现在代码已经完全使用s7connector库，不会再有导入错误！
