package org.jeecg.modules.shebei.vo;

import lombok.Data;
import org.jeecg.modules.shebei.entity.DeviceMonitor;

import java.util.List;
import java.util.Map;

/**
 * 设备监控大屏数据VO
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class DeviceMonitorDashboardVO {
    
    /**
     * 设备状态统计
     */
    private List<DeviceStatusStatVO> deviceStatusStat;
    
    /**
     * 设备运行状态统计
     */
    private List<Map<String, Object>> deviceRunStatusStat;
    
    /**
     * 设备类型统计
     */
    private List<Map<String, Object>> deviceTypeStat;
    
    /**
     * 在线设备列表
     */
    private List<DeviceMonitor> onlineDevices;
    
    /**
     * 异常设备列表
     */
    private List<DeviceMonitor> abnormalDevices;
    
    /**
     * 监测数据分析
     */
    private List<Map<String, Object>> monitorDataAnalysis;
    
    /**
     * 总设备数
     */
    private Integer totalDevices;
    
    /**
     * 在线设备数
     */
    private Integer onlineDevicesCount;
    
    /**
     * 异常设备数
     */
    private Integer abnormalDevicesCount;
    
    /**
     * 设备在线率
     */
    private String onlineRate;
}
