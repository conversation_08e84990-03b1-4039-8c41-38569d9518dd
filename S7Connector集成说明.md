# S7Connector集成说明

## 📦 依赖配置

我已经将代码修改为使用官方的s7connector库。您需要在项目中添加以下依赖：

### Maven依赖

在 `jeecg-boot/jeecg-module-system/jeecg-system-biz/pom.xml` 中添加：

```xml
<dependencies>
    <!-- S7Connector官方库 -->
    <dependency>
        <groupId>com.github.s7connector</groupId>
        <artifactId>s7connector</artifactId>
        <version>2.1</version>
    </dependency>
</dependencies>
```

### 备选方案

如果上述依赖无法下载，可以使用Moka7库：

```xml
<dependency>
    <groupId>com.github.comtel2000</groupId>
    <artifactId>moka7</artifactId>
    <version>1.3.0</version>
</dependency>
```

## 🔧 代码修改说明

### 1. 新增S7PlcUtil工具类

创建了 `S7PlcUtil.java` 工具类，封装了所有S7协议相关操作：

- **连接管理**：自动管理PLC连接池
- **数据读取**：支持所有S7数据类型和区域
- **连接测试**：提供连接测试功能
- **资源管理**：自动管理连接资源

### 2. 修改PlcDataCollectionServiceImpl

简化了服务实现类，移除了自定义的S7协议实现，直接使用S7PlcUtil：

```java
// 原来的复杂实现
S7PlcClient client = new S7PlcClient(plcConfig);
Object value = client.readDataPoint(dataPoint);

// 现在的简化实现
Object value = S7PlcUtil.readDataPoint(plcConfig, dataPoint);
```

## 🚀 使用方法

### 1. 测试PLC连接

```java
PlcConfig plcConfig = new PlcConfig();
plcConfig.setIpAddress("*************");
plcConfig.setRack(0);
plcConfig.setSlot(1);

boolean connected = S7PlcUtil.testConnection(plcConfig);
```

### 2. 读取数据点位

```java
PlcDataPoint dataPoint = new PlcDataPoint();
dataPoint.setDataArea("DB");
dataPoint.setDbNumber(1);
dataPoint.setStartAddress(0);
dataPoint.setDataType("REAL");

Object value = S7PlcUtil.readDataPoint(plcConfig, dataPoint);
```

### 3. 支持的数据类型

| 数据类型 | Java类型 | 说明 |
|---------|---------|------|
| BOOL | Boolean | 布尔值 |
| BYTE | Integer | 无符号字节 |
| WORD | Integer | 无符号字 |
| DWORD | Long | 无符号双字 |
| INT | Short | 有符号整数 |
| DINT | Integer | 有符号双整数 |
| REAL | Float | 32位浮点数 |
| STRING | String | 字符串 |

### 4. 支持的数据区域

| 区域 | 说明 | 示例地址 |
|-----|------|---------|
| DB | 数据块 | DB1.DBD0 |
| I | 输入区 | I0.0 |
| Q | 输出区 | Q0.0 |
| M | 内存区 | M0.0 |

## 🔍 配置示例

### PLC配置
```sql
INSERT INTO plc_config (
    id, plc_name, ip_address, port, rack, slot,
    connection_timeout, read_timeout, enabled
) VALUES (
    'plc_001', '变电站PLC', '*************', 102, 0, 1,
    5000, 3000, 1
);
```

### 数据点位配置
```sql
-- 读取DB1.DBD0的REAL类型数据
INSERT INTO plc_data_point (
    id, plc_id, device_id, point_name, point_desc,
    data_area, db_number, start_address, data_type,
    unit, monitor_item, enabled
) VALUES (
    'point_001', 'plc_001', 'device_001', 'voltage', '电压',
    'DB', 1, 0, 'REAL',
    'V', '电压', 1
);
```

## ⚡ 性能优化

### 1. 连接池管理
- 自动管理PLC连接，避免频繁连接断开
- 连接失效时自动重连
- 支持多PLC并发访问

### 2. 批量读取
- 同一设备的多个数据点位使用同一连接
- 减少网络开销
- 提高数据采集效率

### 3. 异常处理
- 完善的异常处理机制
- 连接失败自动重试
- 详细的错误日志记录

## 🛠️ 调试和测试

### 1. 启用详细日志
```yaml
logging:
  level:
    org.jeecg.modules.shebei: DEBUG
    Moka7: DEBUG
```

### 2. 测试连接
在PLC配置管理页面点击"测试连接"按钮，或者调用API：
```http
POST /shebei/plcConfig/testConnection?id=plc_001
```

### 3. 测试数据读取
在数据点位配置页面点击"测试读取"按钮，或者调用API：
```http
POST /shebei/plcDataPoint/testRead?id=point_001
```

## 🚨 注意事项

### 1. 网络配置
- 确保服务器能访问PLC的IP地址
- 开放S7协议端口（默认102）
- 检查防火墙设置

### 2. PLC配置
- 在PLC中启用S7通信功能
- 确保数据块存在且可访问
- 设置正确的访问权限

### 3. 地址配置
- 确保地址配置正确
- 注意数据类型对齐
- 验证DB块号和地址范围

### 4. 性能考虑
- 合理设置采集周期
- 避免过于频繁的数据读取
- 监控PLC负载情况

## 📋 故障排除

### 1. 连接失败
```
错误：PLC连接测试失败, 错误码: 1
解决：检查IP地址、端口、机架号、插槽号配置
```

### 2. 数据读取失败
```
错误：读取PLC数据失败, 错误码: 5
解决：检查数据块是否存在，地址是否正确
```

### 3. 数据类型错误
```
错误：解析数据异常
解决：确认PLC中的数据类型与配置一致
```

## 🎯 优势

使用官方s7connector库的优势：

1. **稳定可靠**：经过充分测试的成熟库
2. **功能完整**：支持完整的S7协议功能
3. **性能优化**：高效的数据传输和处理
4. **易于维护**：标准化的API接口
5. **社区支持**：活跃的开源社区支持

现在您的系统已经使用官方的s7connector库来与西门子PLC通信，提供了更稳定和高效的数据采集功能。
