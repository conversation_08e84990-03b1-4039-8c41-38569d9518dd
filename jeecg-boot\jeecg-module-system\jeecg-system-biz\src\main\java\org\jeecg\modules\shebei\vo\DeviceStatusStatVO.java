package org.jeecg.modules.shebei.vo;

import lombok.Data;

/**
 * 设备状态统计VO
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class DeviceStatusStatVO {
    
    /**
     * 状态：0-离线，1-在线，2-故障，3-维护
     */
    private Integer status;
    
    /**
     * 数量
     */
    private Integer count;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    public String getStatusName() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "离线";
            case 1:
                return "在线";
            case 2:
                return "故障";
            case 3:
                return "维护";
            default:
                return "未知";
        }
    }
}
