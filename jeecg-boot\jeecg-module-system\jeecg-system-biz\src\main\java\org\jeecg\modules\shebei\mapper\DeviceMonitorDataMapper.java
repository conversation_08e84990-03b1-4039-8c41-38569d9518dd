package org.jeecg.modules.shebei.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.shebei.entity.DeviceMonitorData;

import java.util.List;
import java.util.Map;

/**
 * 设备监控数据Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Mapper
public interface DeviceMonitorDataMapper extends BaseMapper<DeviceMonitorData> {
    
    /**
     * 获取设备最新监控数据
     */
    @Select("SELECT * FROM device_monitor_data WHERE device_id = #{deviceId} " +
            "AND monitor_time = (SELECT MAX(monitor_time) FROM device_monitor_data WHERE device_id = #{deviceId})")
    List<DeviceMonitorData> getLatestDataByDeviceId(@Param("deviceId") String deviceId);
    
    /**
     * 获取设备历史监控数据
     */
    @Select("SELECT * FROM device_monitor_data WHERE device_id = #{deviceId} " +
            "AND monitor_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR) " +
            "ORDER BY monitor_time DESC")
    List<DeviceMonitorData> getHistoryDataByDeviceId(@Param("deviceId") String deviceId, @Param("hours") Integer hours);
    
    /**
     * 获取监测数据分析统计
     */
    @Select("SELECT monitor_item, " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as normal_count, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as warning_count, " +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as error_count " +
            "FROM device_monitor_data " +
            "WHERE monitor_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR) " +
            "GROUP BY monitor_item")
    List<Map<String, Object>> getMonitorDataAnalysis();
    
    /**
     * 获取设备监控趋势数据
     */
    @Select("SELECT DATE_FORMAT(monitor_time, '%H:%i') as time_label, " +
            "monitor_item, " +
            "AVG(monitor_value) as avg_value " +
            "FROM device_monitor_data " +
            "WHERE device_id = #{deviceId} " +
            "AND monitor_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR) " +
            "GROUP BY DATE_FORMAT(monitor_time, '%H:%i'), monitor_item " +
            "ORDER BY monitor_time")
    List<Map<String, Object>> getDeviceTrendData(@Param("deviceId") String deviceId, @Param("hours") Integer hours);
}
