<#list subTables as subTab>
#segment#${subTab.entityName}Modal.vue
<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="40%">
         <BasicForm @register="registerForm"/>
     </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref,inject} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import {${subTab.entityName?uncap_first}FormSchema} from '../${entityName}.data';
    import {${subTab.entityName?uncap_first}Save} from '../${entityName}.api';
    // Emits声明
    const emit = defineEmits(['register','success']);
    //接收主表id
    const mainId = inject('mainId');
    const isUpdate = ref(true);
    //表单配置
    const [registerForm, {resetFields, setFieldsValue, validate}] = useForm({
        labelWidth: 150,
        schemas: ${subTab.entityName?uncap_first}FormSchema,
        showActionButtonGroup: false,
    });
    //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await resetFields();
        setModalProps({confirmLoading: false});
        isUpdate.value = !!data?.isUpdate;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
        }
    });
    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
    //表单提交事件
    async function handleSubmit(v) {
        try {
            let values = await validate();
            setModalProps({confirmLoading: true});
            if (unref(mainId)) {
                values['${subTab.foreignKeys[0]?uncap_first}'] = unref(mainId);
            }
            //提交表单
            await ${subTab.entityName?uncap_first}Save(values, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } finally {
            setModalProps({confirmLoading: false});
        }
    }
</script>

<style scoped>

</style>
</#list>