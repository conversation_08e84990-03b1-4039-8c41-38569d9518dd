# 设备监控系统使用说明

## 系统概述

设备监控系统是基于JeecgBoot框架开发的企业级设备状态监控平台，提供实时设备状态监控、数据分析、大屏展示等功能。

## 功能特性

### 1. 设备监控大屏
- 实时显示设备状态统计
- 3D设备模型展示
- 设备运行信息监控
- 监测数据分析
- 异常设备告警

### 2. 设备管理
- 设备基础信息管理
- 设备状态实时更新
- 设备类型分类管理
- 设备位置定位

### 3. 数据监控
- 实时监控数据采集
- 历史数据查询
- 趋势分析图表
- 异常数据告警

## 安装部署

### 1. 数据库初始化

执行数据库初始化脚本：

```sql
-- 执行 jeecg-boot/db/device_monitor_init.sql
source jeecg-boot/db/device_monitor_init.sql;
```

### 2. 后端配置

确保后端项目包含以下模块：
- `org.jeecg.modules.shebei.entity` - 实体类
- `org.jeecg.modules.shebei.mapper` - 数据访问层
- `org.jeecg.modules.shebei.service` - 业务逻辑层
- `org.jeecg.modules.shebei.controller` - 控制器层

### 3. 前端配置

前端文件位置：
- `jeecgboot-vue3/src/views/shebei/monitor/` - 监控页面
- `DeviceMonitorDashboard.vue` - 监控大屏
- `DeviceMonitorList.vue` - 设备管理列表
- `api.ts` - API接口定义

### 4. 菜单配置

系统会自动创建以下菜单：
- 设备监控（主菜单）
  - 设备监控管理
  - 监控大屏

## 使用指南

### 1. 设备管理

#### 添加设备
1. 进入"设备监控管理"页面
2. 点击"新增"按钮
3. 填写设备基础信息：
   - 设备编号（必填，唯一）
   - 设备名称（必填）
   - 设备类型（变压器、开关柜、电容器、电抗器）
   - 设备状态（离线、在线、故障、维护）
   - 运行状态（停止、运行、异常）
   - 设备位置
   - 技术参数（电压、电流、温度等）

#### 编辑设备
1. 在设备列表中找到目标设备
2. 点击"编辑"按钮
3. 修改设备信息并保存

#### 删除设备
1. 选择要删除的设备
2. 点击"删除"按钮确认删除
3. 支持批量删除操作

### 2. 监控大屏

#### 访问大屏
1. 点击"监控大屏"菜单
2. 或在设备管理页面点击"监控大屏"按钮

#### 大屏功能
- **设备状态统计**：显示在线、离线、故障、维护设备数量
- **设备列表**：显示在线设备列表，点击可查看详情
- **3D设备模型**：中央展示3D设备模型，带有状态指示灯
- **运行信息**：显示电压、电流、温度等实时数据
- **监测数据分析**：统计各监测项目的正常、预警、异常数量
- **维护计划**：显示设备维护计划和历史记录

### 3. 数据监控

#### 实时数据
- 系统自动采集设备实时数据
- 支持电压、电流、温度等参数监控
- 异常数据自动告警

#### 历史数据
- 查询指定时间段的历史数据
- 支持数据导出功能
- 趋势分析图表展示

## API接口

### 设备管理接口

```typescript
// 获取设备列表
GET /shebei/deviceMonitor/list

// 添加设备
POST /shebei/deviceMonitor/add

// 编辑设备
PUT /shebei/deviceMonitor/edit

// 删除设备
DELETE /shebei/deviceMonitor/delete

// 获取大屏数据
GET /shebei/deviceMonitor/dashboard

// 获取设备最新数据
GET /shebei/deviceMonitor/latestData

// 获取历史数据
GET /shebei/deviceMonitor/historyData

// 获取趋势数据
GET /shebei/deviceMonitor/trendData
```

## 数据字典

### 设备类型 (device_type)
- transformer: 变压器
- switchgear: 开关柜
- capacitor: 电容器
- reactor: 电抗器

### 设备状态 (device_status)
- 0: 离线
- 1: 在线
- 2: 故障
- 3: 维护

### 运行状态 (device_run_status)
- 0: 停止
- 1: 运行
- 2: 异常

## 技术架构

### 后端技术栈
- Spring Boot 2.7.18
- MyBatis Plus *******
- MySQL 5.7+

### 前端技术栈
- Vue 3.0
- TypeScript
- Ant Design Vue 4
- ECharts 5.0

## 注意事项

1. **数据库权限**：确保数据库用户有创建表和插入数据的权限
2. **菜单权限**：管理员需要为用户分配相应的菜单和按钮权限
3. **实时数据**：大屏数据每30秒自动刷新一次
4. **浏览器兼容**：建议使用Chrome、Firefox等现代浏览器
5. **分辨率**：监控大屏建议在1920x1080及以上分辨率下使用

## 扩展开发

### 添加新的监控项目
1. 在`device_monitor_data`表中添加新的监控项目
2. 更新前端显示逻辑
3. 配置告警规则

### 自定义大屏样式
1. 修改`DeviceMonitorDashboard.vue`中的CSS样式
2. 调整布局和颜色主题
3. 添加新的图表组件

### 集成外部系统
1. 通过API接口对接外部监控系统
2. 实现数据同步和状态更新
3. 配置告警通知机制

## 联系支持

如有问题或建议，请联系开发团队。
