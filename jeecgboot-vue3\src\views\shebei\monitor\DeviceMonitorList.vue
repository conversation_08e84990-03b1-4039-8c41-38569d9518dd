<template>
  <div>
    <!-- 查询区域 -->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="8">
            <a-form-item label="设备编号" name="deviceCode">
              <a-input placeholder="请输入设备编号" v-model:value="queryParam.deviceCode"></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item label="设备名称" name="deviceName">
              <a-input placeholder="请输入设备名称" v-model:value="queryParam.deviceName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item label="设备状态" name="status">
              <a-select placeholder="请选择设备状态" v-model:value="queryParam.status" allowClear>
                <a-select-option value="0">离线</a-select-option>
                <a-select-option value="1">在线</a-select-option>
                <a-select-option value="2">故障</a-select-option>
                <a-select-option value="3">维护</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :lg="24">
            <span style="float: left; overflow: hidden;" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                <a @click="handleToggleSearch" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <span v-if="toggleSearchStatus">↑</span>
                  <span v-else>↓</span>
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="jeecg-basic-table-form-container">
      <div class="anty-form-btn">
        <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
        <a-button type="primary" icon="download" @click="handleExportXls('设备监控')">导出</a-button>
        <a-button type="primary" icon="upload" @click="handleImportXls">导入</a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>批量操作 ▼</a-button>
        </a-dropdown>
        <a-button @click="handleDashboard" type="primary" icon="dashboard" style="margin-left: 8px">监控大屏</a-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="jeecg-basic-table-container">
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template #htmlSlot="{ text }">
          <div v-html="text"></div>
        </template>
        <template #pcaSlot="{ text }">
          <div>{{ getAreaTextByCode(text) }}</div>
        </template>
        <template #fileSlot="{ text }">
          <span v-if="!text" style="font-size: 12px; font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <template #action="{ record }">
          <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
        </template>

      </a-table>
    </div>

    <!-- 表单区域 -->
    <DeviceMonitorModal ref="modalRef" @success="handleSuccess"></DeviceMonitorModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue'
  import { useMessage } from '/@/hooks/web/useMessage'
  import { useRouter } from 'vue-router'
  import DeviceMonitorModal from './components/DeviceMonitorModal.vue'
  import { columns } from './DeviceMonitor.data'
  import { getDeviceMonitorList, deleteDeviceMonitor, batchDeleteDeviceMonitor } from './api'

  const router = useRouter()
  const { createMessage } = useMessage()

  // 响应式数据
  const loading = ref(false)
  const dataSource = ref([])
  const selectedRowKeys = ref([])
  const toggleSearchStatus = ref(false)
  const modalRef = ref()

  // 查询参数
  const queryParam = reactive({
    deviceCode: '',
    deviceName: '',
    status: undefined,
  })

  // 分页参数
  const ipagination = reactive({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30'],
    showTotal: (total, range) => {
      return range[0] + '-' + range[1] + ' 共' + total + '条'
    },
    showQuickJumper: true,
    showSizeChanger: true,
    total: 0
  })

  // 表单配置
  const labelCol = { span: 5 }
  const wrapperCol = { span: 18, offset: 1 }

  // 加载数据
  const loadData = async (arg?: any) => {
    if (arg === 1) {
      ipagination.current = 1
    }
    const params = Object.assign({}, queryParam, {
      pageNo: ipagination.current,
      pageSize: ipagination.pageSize,
    })
    loading.value = true
    try {
      const result = await getDeviceMonitorList(params)
      if (result.success) {
        dataSource.value = result.result.records
        ipagination.total = result.result.total
      }
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 查询
  const searchQuery = () => {
    loadData(1)
  }

  // 重置
  const searchReset = () => {
    Object.keys(queryParam).forEach(key => {
      queryParam[key] = undefined
    })
    loadData(1)
  }

  // 切换搜索状态
  const handleToggleSearch = () => {
    toggleSearchStatus.value = !toggleSearchStatus.value
  }

  // 表格变化
  const handleTableChange = (pagination) => {
    ipagination.current = pagination.current
    ipagination.pageSize = pagination.pageSize
    loadData()
  }

  // 选择变化
  const onSelectChange = (keys) => {
    selectedRowKeys.value = keys
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    modalRef.value.add()
  }

  /**
   * 编辑事件
   */
  function handleEdit(record) {
    modalRef.value.edit(record)
  }

  /**
   * 详情
   */
  function handleDetail(record) {
    modalRef.value.detail(record)
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    try {
      await deleteDeviceMonitor({ id: record.id })
      createMessage.success('删除成功!')
      handleSuccess()
    } catch (error) {
      createMessage.error('删除失败!')
    }
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    try {
      await batchDeleteDeviceMonitor({ ids: selectedRowKeys.value.join(',') })
      createMessage.success('批量删除成功!')
      handleSuccess()
    } catch (error) {
      createMessage.error('批量删除失败!')
    }
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    selectedRowKeys.value = []
    loadData()
  }

  // 导出
  const handleExportXls = (fileName) => {
    // 导出逻辑
    console.log('导出:', fileName)
  }

  // 导入
  const handleImportXls = () => {
    // 导入逻辑
    console.log('导入')
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      }
    ]
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }, {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          placement: 'topLeft',
          confirm: handleDelete.bind(null, record),
        }
      }
    ]
  }

  /**
   * 跳转到监控大屏
   */
  function handleDashboard() {
    router.push('/shebei/monitor/dashboard')
  }

  // 初始化
  loadData()
</script>

<style scoped>

</style>
