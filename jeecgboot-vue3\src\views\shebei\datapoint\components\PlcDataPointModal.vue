<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit" width="800px">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue'
  import { BasicModal, useModalInner } from '/@/components/Modal'
  import { BasicForm, useForm } from '/@/components/Form/index'
  import { formSchema } from '../PlcDataPoint.data'
  import { addPlcDataPoint, editPlcDataPoint } from '../api'

  // Emits
  const emit = defineEmits(['success', 'register'])

  const isUpdate = ref(true)
  const rowId = ref('')

  // 表单配置
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 120,
    baseColProps: { span: 12 },
    schemas: formSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  })

  // 弹窗配置
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    // 重置表单
    await resetFields()
    setModalProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter })
    isUpdate.value = !!data?.isUpdate

    if (unref(isUpdate)) {
      rowId.value = data.record.id
      // 设置表单值
      await setFieldsValue({
        ...data.record,
      })
    }
  })

  // 弹窗标题
  const getTitle = computed(() => (!unref(isUpdate) ? '新增数据点位' : '编辑数据点位'))

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate()
      setModalProps({ confirmLoading: true })
      
      // 提交表单
      if (unref(isUpdate)) {
        await editPlcDataPoint({ ...values, id: rowId.value })
      } else {
        await addPlcDataPoint(values)
      }
      
      // 关闭弹窗
      closeModal()
      emit('success', { isUpdate: unref(isUpdate), values: { ...values, id: rowId.value } })
    } finally {
      setModalProps({ confirmLoading: false })
    }
  }

  // 暴露方法给父组件
  defineExpose({
    add: () => {
      registerModal({ isUpdate: false, showFooter: true })
    },
    edit: (record: any) => {
      registerModal({ record, isUpdate: true, showFooter: true })
    }
  })
</script>
