package org.jeecg.modules.shebei.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.shebei.entity.PlcConfig;
import org.jeecg.modules.shebei.entity.PlcDataPoint;

// 使用官方s7connector库
import Moka7.*;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * S7协议PLC工具类
 * 基于官方s7connector库实现
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
public class S7PlcUtil {
    
    // PLC连接池
    private static final Map<String, S7Client> clientPool = new ConcurrentHashMap<>();
    
    /**
     * 获取或创建PLC客户端
     */
    public static S7Client getPlcClient(PlcConfig plcConfig) {
        String key = plcConfig.getId();
        S7Client client = clientPool.get(key);
        
        if (client == null || !client.Connected) {
            if (client != null) {
                client.Disconnect();
            }
            
            client = new S7Client();
            int result = client.ConnectTo(
                plcConfig.getIpAddress(), 
                plcConfig.getRack(), 
                plcConfig.getSlot()
            );
            
            if (result == 0) {
                clientPool.put(key, client);
                log.debug("PLC客户端连接成功: {}", plcConfig.getIpAddress());
            } else {
                log.error("PLC客户端连接失败: {}, 错误码: {}", plcConfig.getIpAddress(), result);
                return null;
            }
        }
        
        return client;
    }
    
    /**
     * 测试PLC连接
     */
    public static boolean testConnection(PlcConfig plcConfig) {
        S7Client client = null;
        try {
            client = new S7Client();
            
            // 连接到PLC
            int result = client.ConnectTo(
                plcConfig.getIpAddress(), 
                plcConfig.getRack(), 
                plcConfig.getSlot()
            );
            
            boolean connected = (result == 0);
            
            if (connected) {
                log.info("PLC连接测试成功: {}", plcConfig.getIpAddress());
            } else {
                log.warn("PLC连接测试失败: {}, 错误码: {}", plcConfig.getIpAddress(), result);
            }
            
            return connected;
        } catch (Exception e) {
            log.error("测试PLC连接异常: {}", plcConfig.getIpAddress(), e);
            return false;
        } finally {
            if (client != null) {
                client.Disconnect();
            }
        }
    }
    
    /**
     * 从PLC读取数据
     */
    public static Object readDataFromPlc(S7Client client, PlcDataPoint dataPoint) {
        try {
            byte[] buffer = new byte[256]; // 缓冲区
            int result = 0;
            
            // 根据数据区域选择读取方法
            switch (dataPoint.getDataArea().toUpperCase()) {
                case "DB":
                    result = client.DBRead(
                        dataPoint.getDbNumber(), 
                        dataPoint.getStartAddress(), 
                        getDataSize(dataPoint.getDataType()), 
                        buffer
                    );
                    break;
                case "I":
                    result = client.EBRead(dataPoint.getStartAddress(), getDataSize(dataPoint.getDataType()), buffer);
                    break;
                case "Q":
                    result = client.ABRead(dataPoint.getStartAddress(), getDataSize(dataPoint.getDataType()), buffer);
                    break;
                case "M":
                    result = client.MBRead(dataPoint.getStartAddress(), getDataSize(dataPoint.getDataType()), buffer);
                    break;
                default:
                    log.warn("不支持的数据区域: {}", dataPoint.getDataArea());
                    return null;
            }
            
            if (result != 0) {
                log.error("读取PLC数据失败, 错误码: {}", result);
                return null;
            }
            
            // 解析数据
            return parseData(buffer, dataPoint);
            
        } catch (Exception e) {
            log.error("读取PLC数据异常", e);
            return null;
        }
    }
    
    /**
     * 解析数据
     */
    private static Object parseData(byte[] buffer, PlcDataPoint dataPoint) {
        try {
            switch (dataPoint.getDataType().toUpperCase()) {
                case "BOOL":
                    int bitOffset = dataPoint.getBitOffset() != null ? dataPoint.getBitOffset() : 0;
                    return S7.GetBitAt(buffer, 0, bitOffset);
                case "BYTE":
                    return S7.GetUSIntAt(buffer, 0);
                case "WORD":
                    return S7.GetUIntAt(buffer, 0);
                case "DWORD":
                    return S7.GetUDIntAt(buffer, 0);
                case "INT":
                    return S7.GetIntAt(buffer, 0);
                case "DINT":
                    return S7.GetDIntAt(buffer, 0);
                case "REAL":
                    return S7.GetRealAt(buffer, 0);
                case "STRING":
                    int maxLen = dataPoint.getStringLength() != null ? dataPoint.getStringLength() : 254;
                    return S7.GetStringAt(buffer, 0, maxLen);
                default:
                    log.warn("不支持的数据类型: {}", dataPoint.getDataType());
                    return null;
            }
        } catch (Exception e) {
            log.error("解析数据异常", e);
            return null;
        }
    }
    
    /**
     * 获取数据大小
     */
    private static int getDataSize(String dataType) {
        switch (dataType.toUpperCase()) {
            case "BOOL":
            case "BYTE":
                return 1;
            case "WORD":
            case "INT":
                return 2;
            case "DWORD":
            case "DINT":
            case "REAL":
                return 4;
            case "STRING":
                return 256; // 字符串最大长度
            default:
                return 4;
        }
    }
    
    /**
     * 断开PLC连接
     */
    public static void disconnect(String plcId) {
        S7Client client = clientPool.remove(plcId);
        if (client != null) {
            client.Disconnect();
            log.debug("PLC连接已断开: {}", plcId);
        }
    }
    
    /**
     * 断开所有PLC连接
     */
    public static void disconnectAll() {
        for (Map.Entry<String, S7Client> entry : clientPool.entrySet()) {
            try {
                entry.getValue().Disconnect();
                log.debug("PLC连接已断开: {}", entry.getKey());
            } catch (Exception e) {
                log.error("断开PLC连接异常: {}", entry.getKey(), e);
            }
        }
        clientPool.clear();
    }
    
    /**
     * 检查连接状态
     */
    public static boolean isConnected(String plcId) {
        S7Client client = clientPool.get(plcId);
        return client != null && client.Connected;
    }

    /**
     * 读取数据点位（简化方法）
     */
    public static Object readDataPoint(PlcConfig plcConfig, PlcDataPoint dataPoint) {
        S7Client client = getPlcClient(plcConfig);
        if (client == null) {
            return null;
        }
        return readDataFromPlc(client, dataPoint);
    }
}
