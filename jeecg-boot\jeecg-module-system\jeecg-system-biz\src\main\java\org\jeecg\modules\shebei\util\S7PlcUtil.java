package org.jeecg.modules.shebei.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.shebei.entity.PlcConfig;
import org.jeecg.modules.shebei.entity.PlcDataPoint;

// 使用s7connector库
import com.github.s7connector.api.DaveArea;
import com.github.s7connector.api.S7Connector;
import com.github.s7connector.api.factory.S7ConnectorFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * S7协议PLC工具类
 * 基于s7connector库实现
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
public class S7PlcUtil {

    // PLC连接池
    private static final Map<String, S7Connector> connectorPool = new ConcurrentHashMap<>();
    
    /**
     * 获取或创建PLC连接器
     */
    public static S7Connector getPlcConnector(PlcConfig plcConfig) {
        String key = plcConfig.getId();
        S7Connector connector = connectorPool.get(key);

        if (connector == null) {
            try {
                connector = S7ConnectorFactory
                    .buildTCPConnector()
                    .withHost(plcConfig.getIpAddress())
                    .withPort(plcConfig.getPort())
                    .withRack(plcConfig.getRack())
                    .withSlot(plcConfig.getSlot())
                    .build();

                connectorPool.put(key, connector);
                log.debug("PLC连接器创建成功: {}", plcConfig.getIpAddress());
            } catch (Exception e) {
                log.error("PLC连接器创建失败: {}", plcConfig.getIpAddress(), e);
                return null;
            }
        }

        return connector;
    }
    
    /**
     * 测试PLC连接
     */
    public static boolean testConnection(PlcConfig plcConfig) {
        try {
            S7Connector connector = S7ConnectorFactory
                .buildTCPConnector()
                .withHost(plcConfig.getIpAddress())
                .withPort(plcConfig.getPort())
                .withRack(plcConfig.getRack())
                .withSlot(plcConfig.getSlot())
                .build();

            // 尝试读取一个字节来测试连接
            connector.read(DaveArea.DB, 1, 0, 1);

            log.info("PLC连接测试成功: {}", plcConfig.getIpAddress());
            return true;
        } catch (Exception e) {
            log.error("测试PLC连接失败: {}", plcConfig.getIpAddress(), e);
            return false;
        }
    }
    
    /**
     * 从PLC读取数据
     */
    public static Object readDataFromPlc(S7Connector connector, PlcDataPoint dataPoint) {
        try {
            DaveArea area = getDaveArea(dataPoint.getDataArea());
            if (area == null) {
                log.warn("不支持的数据区域: {}", dataPoint.getDataArea());
                return null;
            }

            int dbNumber = dataPoint.getDbNumber() != null ? dataPoint.getDbNumber() : 0;
            int dataSize = getDataSize(dataPoint.getDataType());

            // 读取数据
            byte[] buffer = connector.read(area, dbNumber, dataPoint.getStartAddress(), dataSize);

            // 解析数据
            return parseData(buffer, dataPoint);

        } catch (Exception e) {
            log.error("读取PLC数据异常", e);
            return null;
        }
    }

    /**
     * 获取DaveArea
     */
    private static DaveArea getDaveArea(String dataArea) {
        switch (dataArea.toUpperCase()) {
            case "DB":
                return DaveArea.DB;
            case "I":
                return DaveArea.INPUTS;
            case "Q":
                return DaveArea.OUTPUTS;
            case "M":
                return DaveArea.FLAGS;
            default:
                return null;
        }
    }
    
    /**
     * 解析数据
     */
    private static Object parseData(byte[] buffer, PlcDataPoint dataPoint) {
        try {
            switch (dataPoint.getDataType().toUpperCase()) {
                case "BOOL":
                    int bitOffset = dataPoint.getBitOffset() != null ? dataPoint.getBitOffset() : 0;
                    return (buffer[0] & (1 << bitOffset)) != 0;
                case "BYTE":
                    return buffer[0] & 0xFF;
                case "WORD":
                    return ((buffer[0] & 0xFF) << 8) | (buffer[1] & 0xFF);
                case "DWORD":
                    return ((buffer[0] & 0xFF) << 24) | ((buffer[1] & 0xFF) << 16) |
                           ((buffer[2] & 0xFF) << 8) | (buffer[3] & 0xFF);
                case "INT":
                    return (short)(((buffer[0] & 0xFF) << 8) | (buffer[1] & 0xFF));
                case "DINT":
                    return ((buffer[0] & 0xFF) << 24) | ((buffer[1] & 0xFF) << 16) |
                           ((buffer[2] & 0xFF) << 8) | (buffer[3] & 0xFF);
                case "REAL":
                    int intBits = ((buffer[0] & 0xFF) << 24) | ((buffer[1] & 0xFF) << 16) |
                                  ((buffer[2] & 0xFF) << 8) | (buffer[3] & 0xFF);
                    return Float.intBitsToFloat(intBits);
                case "STRING":
                    int maxLen = dataPoint.getStringLength() != null ? dataPoint.getStringLength() : 254;
                    int actualLen = Math.min(buffer[1] & 0xFF, maxLen);
                    return new String(buffer, 2, actualLen);
                default:
                    log.warn("不支持的数据类型: {}", dataPoint.getDataType());
                    return null;
            }
        } catch (Exception e) {
            log.error("解析数据异常", e);
            return null;
        }
    }
    
    /**
     * 获取数据大小
     */
    private static int getDataSize(String dataType) {
        switch (dataType.toUpperCase()) {
            case "BOOL":
            case "BYTE":
                return 1;
            case "WORD":
            case "INT":
                return 2;
            case "DWORD":
            case "DINT":
            case "REAL":
                return 4;
            case "STRING":
                return 256; // 字符串最大长度
            default:
                return 4;
        }
    }
    
    /**
     * 断开PLC连接
     */
    public static void disconnect(String plcId) {
        S7Connector connector = connectorPool.remove(plcId);
        if (connector != null) {
            try {
                connector.close();
                log.debug("PLC连接已断开: {}", plcId);
            } catch (Exception e) {
                log.error("断开PLC连接异常: {}", plcId, e);
            }
        }
    }

    /**
     * 断开所有PLC连接
     */
    public static void disconnectAll() {
        for (Map.Entry<String, S7Connector> entry : connectorPool.entrySet()) {
            try {
                entry.getValue().close();
                log.debug("PLC连接已断开: {}", entry.getKey());
            } catch (Exception e) {
                log.error("断开PLC连接异常: {}", entry.getKey(), e);
            }
        }
        connectorPool.clear();
    }

    /**
     * 检查连接状态
     */
    public static boolean isConnected(String plcId) {
        S7Connector connector = connectorPool.get(plcId);
        return connector != null;
    }

    /**
     * 读取数据点位（简化方法）
     */
    public static Object readDataPoint(PlcConfig plcConfig, PlcDataPoint dataPoint) {
        S7Connector connector = getPlcConnector(plcConfig);
        if (connector == null) {
            return null;
        }
        return readDataFromPlc(connector, dataPoint);
    }
}
