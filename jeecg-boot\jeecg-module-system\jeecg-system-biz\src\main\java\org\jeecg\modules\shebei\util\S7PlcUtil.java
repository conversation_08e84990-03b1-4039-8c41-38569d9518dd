package org.jeecg.modules.shebei.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.shebei.entity.PlcConfig;
import org.jeecg.modules.shebei.entity.PlcDataPoint;

import com.github.s7connector.api.DaveArea;
import com.github.s7connector.api.S7Connector;
import com.github.s7connector.api.factory.S7ConnectorFactory;

/**
 * S7协议PLC工具类
 * 基于s7connector库实现
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
public class S7PlcUtil {
    
    /**
     * 测试PLC连接
     */
    public static boolean testConnection(PlcConfig plcConfig) {
        try {
            S7Connector connector = S7ConnectorFactory
                .buildTCPConnector()
                .withHost(plcConfig.getIpAddress())
                .withPort(plcConfig.getPort())
                .withRack(plcConfig.getRack())
                .withSlot(plcConfig.getSlot())
                .build();

            // 尝试读取一个字节来测试连接
            connector.read(DaveArea.DB, 1, 0, 1);
            connector.close();

            log.info("PLC连接测试成功: {}", plcConfig.getIpAddress());
            return true;
        } catch (Exception e) {
            log.error("测试PLC连接失败: {}", plcConfig.getIpAddress(), e);
            return false;
        }
    }

    /**
     * 读取数据点位
     */
    public static Object readDataPoint(PlcConfig plcConfig, PlcDataPoint dataPoint) {
        S7Connector connector = null;
        try {
            // 创建连接器
            connector = S7ConnectorFactory
                .buildTCPConnector()
                .withHost(plcConfig.getIpAddress())
                .withPort(plcConfig.getPort())
                .withRack(plcConfig.getRack())
                .withSlot(plcConfig.getSlot())
                .build();

            // 读取数据
            return readDataFromPlc(connector, dataPoint);

        } catch (Exception e) {
            log.error("读取PLC数据点位异常: {}", dataPoint.getPointName(), e);
            return null;
        } finally {
            if (connector != null) {
                try {
                    connector.close();
                } catch (Exception e) {
                    log.warn("关闭PLC连接异常", e);
                }
            }
        }
    }
    
    /**
     * 从PLC读取数据
     */
    private static Object readDataFromPlc(S7Connector connector, PlcDataPoint dataPoint) {
        try {
            DaveArea area = getDaveArea(dataPoint.getDataArea());
            if (area == null) {
                log.warn("不支持的数据区域: {}", dataPoint.getDataArea());
                return null;
            }

            int dbNumber = dataPoint.getDbNumber() != null ? dataPoint.getDbNumber() : 0;
            int dataSize = getDataSize(dataPoint.getDataType());

            // 读取数据
            byte[] buffer = connector.read(area, dbNumber, dataPoint.getStartAddress(), dataSize);

            // 解析数据
            return parseData(buffer, dataPoint);

        } catch (Exception e) {
            log.error("读取PLC数据异常", e);
            return null;
        }
    }

    /**
     * 获取DaveArea
     */
    private static DaveArea getDaveArea(String dataArea) {
        switch (dataArea.toUpperCase()) {
            case "DB":
                return DaveArea.DB;
            case "I":
                return DaveArea.INPUTS;
            case "Q":
                return DaveArea.OUTPUTS;
            case "M":
                return DaveArea.FLAGS;
            default:
                return null;
        }
    }
    
    /**
     * 解析数据
     */
    private static Object parseData(byte[] buffer, PlcDataPoint dataPoint) {
        try {
            switch (dataPoint.getDataType().toUpperCase()) {
                case "BOOL":
                    int bitOffset = dataPoint.getBitOffset() != null ? dataPoint.getBitOffset() : 0;
                    return (buffer[0] & (1 << bitOffset)) != 0;
                case "BYTE":
                    return buffer[0] & 0xFF;
                case "WORD":
                    return ((buffer[0] & 0xFF) << 8) | (buffer[1] & 0xFF);
                case "DWORD":
                    return ((buffer[0] & 0xFF) << 24) | ((buffer[1] & 0xFF) << 16) |
                           ((buffer[2] & 0xFF) << 8) | (buffer[3] & 0xFF);
                case "INT":
                    return (short)(((buffer[0] & 0xFF) << 8) | (buffer[1] & 0xFF));
                case "DINT":
                    return ((buffer[0] & 0xFF) << 24) | ((buffer[1] & 0xFF) << 16) |
                           ((buffer[2] & 0xFF) << 8) | (buffer[3] & 0xFF);
                case "REAL":
                    int intBits = ((buffer[0] & 0xFF) << 24) | ((buffer[1] & 0xFF) << 16) |
                                  ((buffer[2] & 0xFF) << 8) | (buffer[3] & 0xFF);
                    return Float.intBitsToFloat(intBits);
                case "STRING":
                    int maxLen = dataPoint.getStringLength() != null ? dataPoint.getStringLength() : 254;
                    int actualLen = Math.min(buffer[1] & 0xFF, maxLen);
                    return new String(buffer, 2, actualLen);
                default:
                    log.warn("不支持的数据类型: {}", dataPoint.getDataType());
                    return null;
            }
        } catch (Exception e) {
            log.error("解析数据异常", e);
            return null;
        }
    }
    
    /**
     * 获取数据大小
     */
    private static int getDataSize(String dataType) {
        switch (dataType.toUpperCase()) {
            case "BOOL":
            case "BYTE":
                return 1;
            case "WORD":
            case "INT":
                return 2;
            case "DWORD":
            case "DINT":
            case "REAL":
                return 4;
            case "STRING":
                return 256;
            default:
                return 4;
        }
    }
}
