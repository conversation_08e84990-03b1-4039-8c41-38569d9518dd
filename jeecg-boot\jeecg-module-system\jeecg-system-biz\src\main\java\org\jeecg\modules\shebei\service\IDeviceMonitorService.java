package org.jeecg.modules.shebei.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.shebei.entity.DeviceMonitor;
import org.jeecg.modules.shebei.vo.DeviceMonitorDashboardVO;
import org.jeecg.modules.shebei.vo.DeviceStatusStatVO;

import java.util.List;
import java.util.Map;

/**
 * 设备监控Service接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface IDeviceMonitorService extends IService<DeviceMonitor> {
    
    /**
     * 获取设备监控大屏数据
     */
    DeviceMonitorDashboardVO getDashboardData();
    
    /**
     * 获取设备状态统计
     */
    List<DeviceStatusStatVO> getDeviceStatusStat();
    
    /**
     * 获取设备运行状态统计
     */
    List<Map<String, Object>> getDeviceRunStatusStat();
    
    /**
     * 获取设备类型统计
     */
    List<Map<String, Object>> getDeviceTypeStat();
    
    /**
     * 获取在线设备列表
     */
    List<DeviceMonitor> getOnlineDevices(Integer limit);
    
    /**
     * 获取异常设备列表
     */
    List<DeviceMonitor> getAbnormalDevices();
    
    /**
     * 更新设备状态
     */
    boolean updateDeviceStatus(String deviceId, Integer status, Integer runStatus);
    
    /**
     * 批量更新设备最后更新时间
     */
    boolean batchUpdateLastTime(List<String> deviceIds);
}
