package org.jeecg.modules.shebei.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.shebei.entity.PlcConfig;
import org.jeecg.modules.shebei.service.IPlcConfigService;
import org.jeecg.modules.shebei.service.IPlcDataCollectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;

/**
 * PLC配置Controller
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@RestController
@RequestMapping("/shebei/plcConfig")
@Slf4j
public class PlcConfigController {
    
    @Autowired
    private IPlcConfigService plcConfigService;
    
    @Autowired
    private IPlcDataCollectionService plcDataCollectionService;
    
    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    public Result<IPage<PlcConfig>> queryPageList(PlcConfig plcConfig,
                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                  HttpServletRequest req) {
        QueryWrapper<PlcConfig> queryWrapper = QueryGenerator.initQueryWrapper(plcConfig, req.getParameterMap());
        Page<PlcConfig> page = new Page<PlcConfig>(pageNo, pageSize);
        IPage<PlcConfig> pageList = plcConfigService.page(page, queryWrapper);
        return Result.OK(pageList);
    }
    
    /**
     * 添加
     */
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody PlcConfig plcConfig) {
        plcConfig.setCreateTime(new Date());
        plcConfig.setConnectionStatus(0); // 初始状态为未连接
        plcConfigService.save(plcConfig);
        return Result.OK("添加成功！");
    }
    
    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody PlcConfig plcConfig) {
        plcConfig.setUpdateTime(new Date());
        plcConfigService.updateById(plcConfig);
        return Result.OK("编辑成功!");
    }
    
    /**
     * 通过id删除
     */
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        plcConfigService.removeById(id);
        return Result.OK("删除成功!");
    }
    
    /**
     * 批量删除
     */
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.plcConfigService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }
    
    /**
     * 通过id查询
     */
    @GetMapping(value = "/queryById")
    public Result<PlcConfig> queryById(@RequestParam(name = "id", required = true) String id) {
        PlcConfig plcConfig = plcConfigService.getById(id);
        if (plcConfig == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(plcConfig);
    }
    
    /**
     * 测试PLC连接
     */
    @PostMapping(value = "/testConnection")
    public Result<String> testConnection(@RequestParam(name = "id", required = true) String id) {
        try {
            PlcConfig plcConfig = plcConfigService.getById(id);
            if (plcConfig == null) {
                return Result.error("PLC配置不存在");
            }
            
            boolean connected = plcDataCollectionService.testPlcConnection(plcConfig);
            if (connected) {
                return Result.OK("PLC连接测试成功");
            } else {
                return Result.error("PLC连接测试失败，请检查网络和配置");
            }
        } catch (Exception e) {
            log.error("测试PLC连接异常", e);
            return Result.error("连接测试异常：" + e.getMessage());
        }
    }
    
    /**
     * 启用/禁用PLC
     */
    @PostMapping(value = "/toggleEnabled")
    public Result<String> toggleEnabled(@RequestParam(name = "id", required = true) String id,
                                        @RequestParam(name = "enabled", required = true) Integer enabled) {
        try {
            PlcConfig plcConfig = new PlcConfig();
            plcConfig.setId(id);
            plcConfig.setEnabled(enabled);
            plcConfig.setUpdateTime(new Date());
            plcConfigService.updateById(plcConfig);
            
            String status = enabled == 1 ? "启用" : "禁用";
            return Result.OK("PLC" + status + "成功");
        } catch (Exception e) {
            log.error("切换PLC状态异常", e);
            return Result.error("操作失败：" + e.getMessage());
        }
    }
}
