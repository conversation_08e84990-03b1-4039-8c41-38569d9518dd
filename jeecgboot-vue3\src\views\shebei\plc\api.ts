import { defHttp } from '/@/utils/http/axios'

enum Api {
  // PLC配置相关接口
  PlcConfigList = '/shebei/plcConfig/list',
  PlcConfigAdd = '/shebei/plcConfig/add',
  PlcConfigEdit = '/shebei/plcConfig/edit',
  PlcConfigDelete = '/shebei/plcConfig/delete',
  PlcConfigDeleteBatch = '/shebei/plcConfig/deleteBatch',
  PlcConfigQueryById = '/shebei/plcConfig/queryById',
  PlcConfigTestConnection = '/shebei/plcConfig/testConnection',
  PlcConfigToggleEnabled = '/shebei/plcConfig/toggleEnabled',
}

/**
 * 获取PLC配置列表
 */
export const getPlcConfigList = (params: any) => {
  return defHttp.get({ url: Api.PlcConfigList, params })
}

/**
 * 添加PLC配置
 */
export const addPlcConfig = (params: any) => {
  return defHttp.post({ url: Api.PlcConfigAdd, params })
}

/**
 * 编辑PLC配置
 */
export const editPlcConfig = (params: any) => {
  return defHttp.put({ url: Api.PlcConfigEdit, params })
}

/**
 * 删除PLC配置
 */
export const deletePlcConfig = (params: any) => {
  return defHttp.delete({ url: Api.PlcConfigDelete, params })
}

/**
 * 批量删除PLC配置
 */
export const batchDeletePlcConfig = (params: any) => {
  return defHttp.delete({ url: Api.PlcConfigDeleteBatch, params })
}

/**
 * 通过ID查询PLC配置
 */
export const getPlcConfigById = (params: any) => {
  return defHttp.get({ url: Api.PlcConfigQueryById, params })
}

/**
 * 测试PLC连接
 */
export const testPlcConnection = (id: string) => {
  return defHttp.post({ url: Api.PlcConfigTestConnection, params: { id } })
}

/**
 * 启用/禁用PLC
 */
export const togglePlcEnabled = (id: string, enabled: number) => {
  return defHttp.post({ url: Api.PlcConfigToggleEnabled, params: { id, enabled } })
}
