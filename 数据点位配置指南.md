# 数据点位配置指南

## 🎯 配置位置

数据点位配置现在可以通过管理界面进行：

### 访问路径
- **菜单路径**：`设备监控` → `数据点位配置`
- **直接访问**：`/shebei/datapoint/config`

## 📋 配置步骤

### 1. 前提条件
在配置数据点位之前，请确保：
- ✅ 已配置好PLC连接（在PLC配置管理中）
- ✅ 已添加设备信息（在设备监控管理中）
- ✅ 了解PLC中数据的存储位置

### 2. 添加数据点位

点击"新增数据点位"按钮，填写以下信息：

#### 基本信息
- **PLC配置**：选择对应的PLC
- **设备**：选择要监控的设备
- **点位名称**：英文名称，如：`voltage`、`current`、`temperature`
- **点位描述**：中文描述，如：`电压`、`电流`、`温度`

#### 地址配置
- **数据区域**：选择数据存储区域
  - `DB` - 数据块（最常用）
  - `I` - 输入区域
  - `Q` - 输出区域
  - `M` - 内存区域
  - `T` - 定时器
  - `C` - 计数器

- **DB块号**：如果选择DB区域，输入数据块编号
- **起始地址**：数据在块中的字节偏移地址
- **数据类型**：选择对应的数据类型

#### 数据处理
- **数据单位**：如：`V`、`A`、`℃`、`%`
- **缩放因子**：数据缩放倍数（默认1）
- **偏移量**：数据偏移值（默认0）
- **监控项目**：对应的监控项目名称

#### 范围设置
- **最小值**：正常范围的最小值
- **最大值**：正常范围的最大值
- **采集周期**：数据采集间隔（秒）

## 🔧 常见配置示例

### 示例1：变压器电压监控

```
PLC配置：变电站主控PLC
设备：主变压器A
点位名称：voltage
点位描述：一次侧电压
数据区域：DB
DB块号：1
起始地址：0
数据类型：REAL
数据单位：kV
缩放因子：0.001
偏移量：0
监控项目：电压
最小值：0
最大值：550
采集周期：30
```

### 示例2：电流监控

```
PLC配置：变电站主控PLC
设备：主变压器A
点位名称：current
点位描述：一次侧电流
数据区域：DB
DB块号：1
起始地址：4
数据类型：REAL
数据单位：A
缩放因子：1
偏移量：0
监控项目：电流
最小值：0
最大值：1000
采集周期：30
```

### 示例3：温度监控

```
PLC配置：变电站主控PLC
设备：主变压器A
点位名称：oil_temp
点位描述：油温
数据区域：DB
DB块号：1
起始地址：8
数据类型：REAL
数据单位：℃
缩放因子：1
偏移量：0
监控项目：温度
最小值：-40
最大值：120
采集周期：30
```

### 示例4：开关状态监控

```
PLC配置：变电站主控PLC
设备：开关柜1号
点位名称：switch_status
点位描述：开关状态
数据区域：DB
DB块号：2
起始地址：0
数据类型：BOOL
位偏移：0
数据单位：无
缩放因子：1
偏移量：0
监控项目：开关状态
最小值：0
最大值：1
采集周期：10
```

## 📊 数据类型说明

| 数据类型 | 字节长度 | 取值范围 | 用途 |
|---------|---------|---------|------|
| BOOL | 1 bit | 0/1 | 开关状态、报警状态 |
| BYTE | 1 字节 | 0-255 | 小整数值 |
| WORD | 2 字节 | 0-65535 | 无符号整数 |
| DWORD | 4 字节 | 0-4294967295 | 大整数值 |
| INT | 2 字节 | -32768~32767 | 有符号整数 |
| DINT | 4 字节 | -2147483648~2147483647 | 大有符号整数 |
| REAL | 4 字节 | ±3.4E±38 | 浮点数（电压、电流等） |
| STRING | 可变 | 字符串 | 文本信息 |

## 🔍 地址计算说明

### DB块地址计算
- **BOOL类型**：`DB1.DBX0.0` → DB块号=1，起始地址=0，位偏移=0
- **BYTE类型**：`DB1.DBB0` → DB块号=1，起始地址=0
- **WORD类型**：`DB1.DBW0` → DB块号=1，起始地址=0
- **DWORD类型**：`DB1.DBD0` → DB块号=1，起始地址=0
- **REAL类型**：`DB1.DBD0` → DB块号=1，起始地址=0

### 地址偏移规则
- BOOL：按位偏移，8个BOOL占用1个字节
- BYTE：按字节偏移，每个BYTE占用1个字节
- WORD/INT：按字偏移，每个占用2个字节
- DWORD/DINT/REAL：按双字偏移，每个占用4个字节

## ✅ 配置验证

### 1. 测试读取
配置完成后，点击"测试读取"按钮验证：
- ✅ 连接正常：显示读取到的数值
- ❌ 连接失败：检查PLC连接状态
- ❌ 地址错误：检查地址配置是否正确

### 2. 查看采集日志
在系统日志中查看数据采集情况：
```
2025-01-01 10:00:00 INFO - 采集设备数据完成: 5 个数据点位
2025-01-01 10:00:30 INFO - 采集设备数据完成: 5 个数据点位
```

### 3. 监控大屏验证
访问监控大屏，确认：
- 设备状态正常更新
- 实时数据显示正确
- 历史数据记录正常

## 🚨 常见问题

### 1. 读取失败
**可能原因**：
- PLC连接断开
- 地址配置错误
- 数据类型不匹配
- PLC中不存在该地址

**解决方法**：
- 检查PLC连接状态
- 确认地址配置正确
- 验证数据类型匹配
- 在PLC编程软件中确认地址存在

### 2. 数据异常
**可能原因**：
- 缩放因子设置错误
- 数据范围设置不当
- PLC数据格式变化

**解决方法**：
- 检查缩放因子和偏移量
- 调整数据范围设置
- 确认PLC数据格式

### 3. 采集频率问题
**可能原因**：
- 采集周期设置过短
- PLC响应速度慢
- 网络延迟

**解决方法**：
- 适当增加采集周期
- 优化PLC程序
- 检查网络状况

## 💡 最佳实践

1. **命名规范**：使用有意义的英文名称
2. **分组管理**：按设备类型分组配置
3. **合理周期**：根据数据重要性设置采集周期
4. **范围设置**：设置合理的数据范围用于异常检测
5. **测试验证**：每个点位配置后都要测试验证

配置完成后，系统将自动按设定的周期从PLC采集数据，并在监控大屏中实时显示。
