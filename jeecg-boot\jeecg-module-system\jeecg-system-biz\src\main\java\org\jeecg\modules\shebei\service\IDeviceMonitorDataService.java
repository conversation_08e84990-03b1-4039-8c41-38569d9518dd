package org.jeecg.modules.shebei.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.shebei.entity.DeviceMonitorData;

import java.util.List;
import java.util.Map;

/**
 * 设备监控数据Service接口
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface IDeviceMonitorDataService extends IService<DeviceMonitorData> {
    
    /**
     * 获取设备最新监控数据
     */
    List<DeviceMonitorData> getLatestDataByDeviceId(String deviceId);
    
    /**
     * 获取设备历史监控数据
     */
    List<DeviceMonitorData> getHistoryDataByDeviceId(String deviceId, Integer hours);
    
    /**
     * 获取监测数据分析统计
     */
    List<Map<String, Object>> getMonitorDataAnalysis();
    
    /**
     * 获取设备监控趋势数据
     */
    List<Map<String, Object>> getDeviceTrendData(String deviceId, Integer hours);
    
    /**
     * 批量保存监控数据
     */
    boolean batchSaveMonitorData(List<DeviceMonitorData> dataList);
}
