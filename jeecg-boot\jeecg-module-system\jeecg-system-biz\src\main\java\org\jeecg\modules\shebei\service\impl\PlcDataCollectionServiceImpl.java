package org.jeecg.modules.shebei.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.shebei.entity.DeviceMonitor;
import org.jeecg.modules.shebei.entity.DeviceMonitorData;
import org.jeecg.modules.shebei.entity.PlcConfig;
import org.jeecg.modules.shebei.entity.PlcDataPoint;
import org.jeecg.modules.shebei.mapper.PlcConfigMapper;
import org.jeecg.modules.shebei.mapper.PlcDataPointMapper;
import org.jeecg.modules.shebei.service.IDeviceMonitorDataService;
import org.jeecg.modules.shebei.service.IDeviceMonitorService;
import org.jeecg.modules.shebei.service.IPlcDataCollectionService;
import org.jeecg.modules.shebei.util.S7PlcUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PLC数据采集服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
@Slf4j
public class PlcDataCollectionServiceImpl implements IPlcDataCollectionService {

    @Autowired
    private PlcConfigMapper plcConfigMapper;

    @Autowired
    private PlcDataPointMapper plcDataPointMapper;

    @Autowired
    private IDeviceMonitorService deviceMonitorService;

    @Autowired
    private IDeviceMonitorDataService deviceMonitorDataService;

    @Override
    public boolean testPlcConnection(PlcConfig plcConfig) {
        try {
            boolean connected = S7PlcUtil.testConnection(plcConfig);

            // 更新连接状态
            updatePlcConnectionStatus(plcConfig.getId(), connected ? 1 : 0);

            return connected;
        } catch (Exception e) {
            log.error("测试PLC连接异常: {}", plcConfig.getIpAddress(), e);
            updatePlcConnectionStatus(plcConfig.getId(), 0);
            return false;
        }
    }

    @Override
    public Object collectSingleDataPoint(PlcDataPoint dataPoint) {
        try {
            // 获取PLC配置
            PlcConfig plcConfig = plcConfigMapper.selectById(dataPoint.getPlcId());
            if (plcConfig == null || plcConfig.getEnabled() != 1) {
                log.warn("PLC配置不存在或未启用: {}", dataPoint.getPlcId());
                return null;
            }

            // 读取数据
            Object value = S7PlcUtil.readDataPoint(plcConfig, dataPoint);

            if (value != null) {
                log.debug("采集数据点位成功: {} = {}", dataPoint.getPointName(), value);
            } else {
                log.warn("采集数据点位失败: {}", dataPoint.getPointName());
            }

            return value;

        } catch (Exception e) {
            log.error("采集数据点位异常: {}", dataPoint.getPointName(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> collectDeviceData(String deviceId) {
        Map<String, Object> deviceData = new HashMap<>();
        
        try {
            // 获取设备的数据点位配置
            List<PlcDataPoint> dataPoints = getDeviceDataPoints(deviceId);
            
            if (dataPoints.isEmpty()) {
                log.warn("设备没有配置数据点位: {}", deviceId);
                return deviceData;
            }
            
            // 按PLC分组
            Map<String, List<PlcDataPoint>> plcGroups = dataPoints.stream()
                    .collect(Collectors.groupingBy(PlcDataPoint::getPlcId));

            for (Map.Entry<String, List<PlcDataPoint>> entry : plcGroups.entrySet()) {
                String plcId = entry.getKey();
                List<PlcDataPoint> points = entry.getValue();

                // 获取PLC配置
                PlcConfig plcConfig = plcConfigMapper.selectById(plcId);
                if (plcConfig == null || plcConfig.getEnabled() != 1) {
                    continue;
                }

                // 逐个读取数据点位
                for (PlcDataPoint point : points) {
                    try {
                        Object value = S7PlcUtil.readDataPoint(plcConfig, point);
                        if (value != null) {
                            // 应用缩放因子和偏移量
                            if (point.getScaleFactor() != null && point.getOffset() != null) {
                                if (value instanceof Number) {
                                    double scaledValue = ((Number) value).doubleValue() * point.getScaleFactor() + point.getOffset();
                                    deviceData.put(point.getPointName(), scaledValue);
                                } else {
                                    deviceData.put(point.getPointName(), value);
                                }
                            } else {
                                deviceData.put(point.getPointName(), value);
                            }
                        }
                    } catch (Exception e) {
                        log.error("读取数据点位失败: {}", point.getPointName(), e);
                    }
                }
            }
            
            log.info("采集设备数据完成: {} 个数据点位", deviceData.size());
            
        } catch (Exception e) {
            log.error("采集设备数据异常: {}", deviceId, e);
        }
        
        return deviceData;
    }
    
    @Override
    public void collectAllDevicesData() {
        try {
            // 获取所有启用的设备
            LambdaQueryWrapper<DeviceMonitor> deviceQuery = new LambdaQueryWrapper<>();
            deviceQuery.eq(DeviceMonitor::getStatus, 1); // 只采集在线设备
            List<DeviceMonitor> devices = deviceMonitorService.list(deviceQuery);
            
            log.info("开始采集所有设备数据，共 {} 个设备", devices.size());
            
            for (DeviceMonitor device : devices) {
                try {
                    // 采集设备数据
                    Map<String, Object> deviceData = collectDeviceData(device.getId());
                    
                    if (!deviceData.isEmpty()) {
                        // 保存采集到的数据
                        saveCollectedData(device.getId(), deviceData);
                        
                        // 更新设备基础参数
                        updateDeviceBasicParams(device, deviceData);
                    }
                    
                } catch (Exception e) {
                    log.error("采集设备数据失败: {}", device.getDeviceName(), e);
                }
            }
            
            log.info("所有设备数据采集完成");
            
        } catch (Exception e) {
            log.error("采集所有设备数据异常", e);
        }
    }
    
    @Override
    public List<PlcConfig> getPlcConfigs() {
        LambdaQueryWrapper<PlcConfig> query = new LambdaQueryWrapper<>();
        query.eq(PlcConfig::getEnabled, 1);
        return plcConfigMapper.selectList(query);
    }
    
    @Override
    public List<PlcDataPoint> getDeviceDataPoints(String deviceId) {
        LambdaQueryWrapper<PlcDataPoint> query = new LambdaQueryWrapper<>();
        query.eq(PlcDataPoint::getDeviceId, deviceId);
        query.eq(PlcDataPoint::getEnabled, 1);
        query.orderByAsc(PlcDataPoint::getCreateTime);
        return plcDataPointMapper.selectList(query);
    }
    
    @Override
    public void updatePlcConnectionStatus(String plcId, Integer status) {
        try {
            PlcConfig plcConfig = new PlcConfig();
            plcConfig.setId(plcId);
            plcConfig.setConnectionStatus(status);
            plcConfig.setLastConnectionTime(new Date());
            plcConfigMapper.updateById(plcConfig);
        } catch (Exception e) {
            log.error("更新PLC连接状态失败: {}", plcId, e);
        }
    }
    
    @Override
    public void saveCollectedData(String deviceId, Map<String, Object> data) {
        try {
            DeviceMonitor device = deviceMonitorService.getById(deviceId);
            if (device == null) {
                return;
            }
            
            Date now = new Date();
            List<DeviceMonitorData> dataList = new ArrayList<>();
            
            // 获取设备的数据点位配置，用于获取监控项目映射
            List<PlcDataPoint> dataPoints = getDeviceDataPoints(deviceId);
            Map<String, PlcDataPoint> pointMap = dataPoints.stream()
                    .collect(Collectors.toMap(PlcDataPoint::getPointName, p -> p));
            
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String pointName = entry.getKey();
                Object value = entry.getValue();
                
                PlcDataPoint dataPoint = pointMap.get(pointName);
                if (dataPoint == null || dataPoint.getMonitorItem() == null) {
                    continue;
                }
                
                DeviceMonitorData monitorData = new DeviceMonitorData();
                monitorData.setDeviceId(deviceId);
                monitorData.setDeviceCode(device.getDeviceCode());
                monitorData.setMonitorItem(dataPoint.getMonitorItem());
                monitorData.setUnit(dataPoint.getUnit());
                monitorData.setMonitorTime(now);
                monitorData.setCreateTime(now);
                
                // 转换数值
                if (value instanceof Number) {
                    BigDecimal monitorValue = new BigDecimal(value.toString());
                    monitorData.setMonitorValue(monitorValue);
                    
                    // 设置正常范围
                    if (dataPoint.getMinValue() != null) {
                        monitorData.setMinValue(new BigDecimal(dataPoint.getMinValue()));
                    }
                    if (dataPoint.getMaxValue() != null) {
                        monitorData.setMaxValue(new BigDecimal(dataPoint.getMaxValue()));
                    }
                    
                    // 判断状态
                    int status = 0; // 正常
                    if (dataPoint.getMinValue() != null && monitorValue.doubleValue() < dataPoint.getMinValue()) {
                        status = 2; // 异常
                    } else if (dataPoint.getMaxValue() != null && monitorValue.doubleValue() > dataPoint.getMaxValue()) {
                        status = 2; // 异常
                    }
                    monitorData.setStatus(status);
                }
                
                dataList.add(monitorData);
            }
            
            // 批量保存监控数据
            if (!dataList.isEmpty()) {
                deviceMonitorDataService.saveBatch(dataList);
                log.debug("保存设备监控数据: {} 条", dataList.size());
            }
            
        } catch (Exception e) {
            log.error("保存采集数据失败: {}", deviceId, e);
        }
    }
    
    /**
     * 更新设备基础参数
     */
    private void updateDeviceBasicParams(DeviceMonitor device, Map<String, Object> data) {
        try {
            boolean updated = false;
            
            // 更新电压
            if (data.containsKey("voltage") && data.get("voltage") instanceof Number) {
                device.setVoltage(new BigDecimal(data.get("voltage").toString()));
                updated = true;
            }
            
            // 更新电流
            if (data.containsKey("current") && data.get("current") instanceof Number) {
                device.setCurrent(new BigDecimal(data.get("current").toString()));
                updated = true;
            }
            
            // 更新温度
            if (data.containsKey("temperature") && data.get("temperature") instanceof Number) {
                device.setTemperature(new BigDecimal(data.get("temperature").toString()));
                updated = true;
            }
            
            if (updated) {
                device.setLastUpdateTime(new Date());
                deviceMonitorService.updateById(device);
            }
            
        } catch (Exception e) {
            log.error("更新设备基础参数失败: {}", device.getId(), e);
        }
    }
}
